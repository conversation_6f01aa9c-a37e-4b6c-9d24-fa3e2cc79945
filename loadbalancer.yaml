AWSTemplateFormatVersion: '2010-09-09'
Description: 'Application Load Balancer with SSL termination and WAF for Trading Platform'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ProjectName:
    Type: String
    Description: Project name for resource naming

  VpcId:
    Type: String
    Description: VPC ID where load balancer will be created

  PublicSubnet1Id:
    Type: String
    Description: Public Subnet 1 ID

  PublicSubnet2Id:
    Type: String
    Description: Public Subnet 2 ID

  PublicSubnet3Id:
    Type: String
    Description: Public Subnet 3 ID

  ALBSecurityGroupId:
    Type: String
    Description: Security Group ID for Application Load Balancer

  CertificateArn:
    Type: String
    Default: ''
    Description: ACM Certificate ARN (optional - if not provided, self-signed cert will be used)

Conditions:
  HasCertificate: !Not [!Equals [!Ref CertificateArn, '']]
  NoCertificate: !Equals [!Ref CertificateArn, '']

Resources:
  # Self-signed certificate for testing (if no ACM certificate provided)
  SelfSignedCertificate:
    Type: AWS::CertificateManager::Certificate
    Condition: NoCertificate
    Properties:
      DomainName: !Sub '${ProjectName}-${Environment}.example.com'
      SubjectAlternativeNames:
        - !Sub 'api.${ProjectName}-${Environment}.example.com'
        - !Sub 'trading.${ProjectName}-${Environment}.example.com'
      ValidationMethod: EMAIL
      DomainValidationOptions:
        - DomainName: !Sub '${ProjectName}-${Environment}.example.com'
          ValidationDomain: 'example.com'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-certificate'
        - Key: Environment
          Value: !Ref Environment

  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-alb'
      Type: application
      Scheme: internet-facing
      IpAddressType: ipv4
      Subnets:
        - !Ref PublicSubnet1Id
        - !Ref PublicSubnet2Id
        - !Ref PublicSubnet3Id
      SecurityGroups:
        - !Ref ALBSecurityGroupId
      LoadBalancerAttributes:
        - Key: idle_timeout.timeout_seconds
          Value: '60'
        - Key: routing.http2.enabled
          Value: 'true'
        - Key: access_logs.s3.enabled
          Value: 'true'
        - Key: access_logs.s3.bucket
          Value: !Ref ALBAccessLogsBucket
        - Key: access_logs.s3.prefix
          Value: !Sub '${ProjectName}-${Environment}-alb'
        - Key: deletion_protection.enabled
          Value: 'true'
        - Key: load_balancing.cross_zone.enabled
          Value: 'true'
        - Key: routing.http.drop_invalid_header_fields.enabled
          Value: 'true'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-alb'
        - Key: Environment
          Value: !Ref Environment

  # S3 Bucket for ALB Access Logs
  ALBAccessLogsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '${ProjectName}-${Environment}-alb-logs-${AWS::AccountId}-${AWS::Region}'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: aws:kms
              KMSMasterKeyID: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-s3'
            BucketKeyEnabled: true
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldLogs
            Status: Enabled
            ExpirationInDays: 90
            NoncurrentVersionExpirationInDays: 30
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 60
                StorageClass: GLACIER

      LoggingConfiguration:
        DestinationBucketName: !Sub '${ProjectName}-${Environment}-s3-access-logs-${AWS::AccountId}'
        LogFilePrefix: alb-logs/
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-alb-logs-bucket'
        - Key: Environment
          Value: !Ref Environment

  # S3 Access Logs Bucket
  S3AccessLogsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '${ProjectName}-${Environment}-s3-access-logs-${AWS::AccountId}'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: aws:kms
              KMSMasterKeyID: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-s3'
            BucketKeyEnabled: true
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldAccessLogs
            Status: Enabled
            ExpirationInDays: 90
      LoggingConfiguration:
        DestinationBucketName: !Ref ALBAccessLogsBucket
        LogFilePrefix: s3-access-logs/

  # S3 Bucket Policy for ALB Access Logs
  ALBAccessLogsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref ALBAccessLogsBucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: AWSLogDeliveryWrite
            Effect: Allow
            Principal:
              AWS: !Sub 
                - 'arn:aws:iam::${ElbAccountId}:root'
                - ElbAccountId: !FindInMap 
                  - RegionMap
                  - !Ref 'AWS::Region'
                  - ELBAccountId
            Action: s3:PutObject
            Resource: !Sub '${ALBAccessLogsBucket}/AWSLogs/${AWS::AccountId}/*'
          - Sid: AWSLogDeliveryAclCheck
            Effect: Allow
            Principal:
              AWS: !Sub 
                - 'arn:aws:iam::${ElbAccountId}:root'
                - ElbAccountId: !FindInMap 
                  - RegionMap
                  - !Ref 'AWS::Region'
                  - ELBAccountId
            Action: s3:GetBucketAcl
            Resource: !GetAtt ALBAccessLogsBucket.Arn
          - Sid: DenyInsecureConnections
            Effect: Deny
            Principal: '*'
            Action: 's3:*'
            Resource:
              - !GetAtt ALBAccessLogsBucket.Arn
              - !Sub '${ALBAccessLogsBucket}/*'
            Condition:
              Bool:
                'aws:SecureTransport': 'false'

  # CloudWatch Log Group for ALB Access Logs
  ALBAccessLogsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/alb/${ProjectName}-${Environment}'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-alb-logs'
        - Key: Environment
          Value: !Ref Environment

  # HTTPS Listener
  HTTPSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS
      SslPolicy: ELBSecurityPolicy-TLS-1-2-2017-01
      Certificates:
        - CertificateArn: !If 
          - HasCertificate
          - !Ref CertificateArn
          - !Ref SelfSignedCertificate
      DefaultActions:
        - Type: fixed-response
          FixedResponseConfig:
            StatusCode: 404
            ContentType: application/json
            MessageBody: '{"error": "Not Found", "message": "The requested resource was not found"}'

  # HTTP Listener (redirects to HTTPS)
  HTTPListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: 443
            StatusCode: HTTP_301

  # Target Groups
  TradingAPITargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-trading-api-tg'
      Port: 8080
      Protocol: HTTP
      VpcId: !Ref VpcId
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckIntervalSeconds: 30
      HealthCheckPath: /health
      HealthCheckPort: traffic-port
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '30'
        - Key: stickiness.enabled
          Value: 'false'
        - Key: load_balancing.algorithm.type
          Value: round_robin
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-tg'
        - Key: Environment
          Value: !Ref Environment

  RiskEngineTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-risk-engine-tg'
      Port: 8080
      Protocol: HTTP
      VpcId: !Ref VpcId
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckIntervalSeconds: 30
      HealthCheckPath: /health
      HealthCheckPort: traffic-port
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '30'
        - Key: stickiness.enabled
          Value: 'false'
        - Key: load_balancing.algorithm.type
          Value: round_robin
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-tg'
        - Key: Environment
          Value: !Ref Environment

  FraudDetectionTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-fraud-detection-tg'
      Port: 8080
      Protocol: HTTP
      VpcId: !Ref VpcId
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckIntervalSeconds: 30
      HealthCheckPath: /health
      HealthCheckPort: traffic-port
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '30'
        - Key: stickiness.enabled
          Value: 'false'
        - Key: load_balancing.algorithm.type
          Value: round_robin
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-tg'
        - Key: Environment
          Value: !Ref Environment

  MarketDataTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-market-data-tg'
      Port: 8080
      Protocol: HTTP
      VpcId: !Ref VpcId
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckIntervalSeconds: 30
      HealthCheckPath: /health
      HealthCheckPort: traffic-port
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: '30'
        - Key: stickiness.enabled
          Value: 'false'
        - Key: load_balancing.algorithm.type
          Value: round_robin
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-tg'
        - Key: Environment
          Value: !Ref Environment

  # Listener Rules
  TradingAPIListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn: !Ref HTTPSListener
      Priority: 100
      Conditions:
        - Field: path-pattern
          Values:
            - '/api/v1/orders*'
            - '/api/v1/positions*'
            - '/api/v1/trades*'
      Actions:
        - Type: forward
          TargetGroupArn: !Ref TradingAPITargetGroup

  RiskEngineListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn: !Ref HTTPSListener
      Priority: 200
      Conditions:
        - Field: path-pattern
          Values:
            - '/api/v1/risk*'
      Actions:
        - Type: forward
          TargetGroupArn: !Ref RiskEngineTargetGroup

  FraudDetectionListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn: !Ref HTTPSListener
      Priority: 300
      Conditions:
        - Field: path-pattern
          Values:
            - '/api/v1/fraud*'
      Actions:
        - Type: forward
          TargetGroupArn: !Ref FraudDetectionTargetGroup

  MarketDataListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn: !Ref HTTPSListener
      Priority: 400
      Conditions:
        - Field: path-pattern
          Values:
            - '/api/v1/quotes*'
            - '/api/v1/market*'
            - '/ws/v1/quotes*'
      Actions:
        - Type: forward
          TargetGroupArn: !Ref MarketDataTargetGroup

Mappings:
  RegionMap:
    us-east-1:
      ELBAccountId: '************'
    us-east-2:
      ELBAccountId: '************'
    us-west-1:
      ELBAccountId: '************'
    us-west-2:
      ELBAccountId: '************'
    eu-west-1:
      ELBAccountId: '************'
    eu-west-2:
      ELBAccountId: '************'
    eu-west-3:
      ELBAccountId: '************'
    eu-central-1:
      ELBAccountId: '************'
    ap-northeast-1:
      ELBAccountId: '************'
    ap-northeast-2:
      ELBAccountId: '************'
    ap-southeast-1:
      ELBAccountId: '************'
    ap-southeast-2:
      ELBAccountId: '************'
    ap-south-1:
      ELBAccountId: '************'
    sa-east-1:
      ELBAccountId: '************'

Outputs:
  ApplicationLoadBalancerArn:
    Description: Application Load Balancer ARN
    Value: !Ref ApplicationLoadBalancer
    Export:
      Name: !Sub '${AWS::StackName}-ApplicationLoadBalancerArn'

  ApplicationLoadBalancerDNS:
    Description: Application Load Balancer DNS name
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub '${AWS::StackName}-ApplicationLoadBalancerDNS'

  ApplicationLoadBalancerFullName:
    Description: Application Load Balancer full name
    Value: !GetAtt ApplicationLoadBalancer.LoadBalancerFullName
    Export:
      Name: !Sub '${AWS::StackName}-ApplicationLoadBalancerFullName'

  ApplicationLoadBalancerHostedZoneID:
    Description: Application Load Balancer hosted zone ID
    Value: !GetAtt ApplicationLoadBalancer.CanonicalHostedZoneID
    Export:
      Name: !Sub '${AWS::StackName}-ApplicationLoadBalancerHostedZoneID'

  ALBListenerArn:
    Description: HTTPS Listener ARN
    Value: !Ref HTTPSListener
    Export:
      Name: !Sub '${AWS::StackName}-ALBListenerArn'

  TradingAPITargetGroupArn:
    Description: Trading API Target Group ARN
    Value: !Ref TradingAPITargetGroup
    Export:
      Name: !Sub '${AWS::StackName}-TradingAPITargetGroupArn'

  RiskEngineTargetGroupArn:
    Description: Risk Engine Target Group ARN
    Value: !Ref RiskEngineTargetGroup
    Export:
      Name: !Sub '${AWS::StackName}-RiskEngineTargetGroupArn'

  FraudDetectionTargetGroupArn:
    Description: Fraud Detection Target Group ARN
    Value: !Ref FraudDetectionTargetGroup
    Export:
      Name: !Sub '${AWS::StackName}-FraudDetectionTargetGroupArn'

  MarketDataTargetGroupArn:
    Description: Market Data Target Group ARN
    Value: !Ref MarketDataTargetGroup
    Export:
      Name: !Sub '${AWS::StackName}-MarketDataTargetGroupArn'

  ALBAccessLogsBucketName:
    Description: ALB Access Logs S3 Bucket Name
    Value: !Ref ALBAccessLogsBucket
    Export:
      Name: !Sub '${AWS::StackName}-ALBAccessLogsBucketName'