AWSTemplateFormatVersion: '2010-09-09'
Description: 'Monitoring and alerting infrastructure for Trading Platform'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ProjectName:
    Type: String
    Description: Project name for resource naming

  ECSClusterName:
    Type: String
    Description: ECS cluster name to monitor

  DatabaseClusterIdentifier:
    Type: String
    Description: Aurora cluster identifier to monitor

  ApplicationLoadBalancerFullName:
    Type: String
    Description: Application Load Balancer full name for monitoring

Resources:
  # SNS Topic for Critical Alerts
  CriticalAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ProjectName}-${Environment}-critical-alerts'
      DisplayName: !Sub 'Critical Alerts for ${ProjectName} ${Environment}'
      KmsMasterKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-secrets'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-critical-alerts'
        - Key: Environment
          Value: !Ref Environment

  # SNS Topic for Warning Alerts
  WarningAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ProjectName}-${Environment}-warning-alerts'
      DisplayName: !Sub 'Warning Alerts for ${ProjectName} ${Environment}'
      KmsMasterKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-secrets'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-warning-alerts'
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Dashboard
  TradingPlatformDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: !Sub '${ProjectName}-${Environment}-dashboard'
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/ApplicationELB", "RequestCount", "LoadBalancer", "${ApplicationLoadBalancerFullName}" ],
                  [ ".", "TargetResponseTime", ".", "." ],
                  [ ".", "HTTPCode_Target_2XX_Count", ".", "." ],
                  [ ".", "HTTPCode_Target_4XX_Count", ".", "." ],
                  [ ".", "HTTPCode_Target_5XX_Count", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Application Load Balancer Metrics",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/ECS", "CPUUtilization", "ServiceName", "${ProjectName}-${Environment}-trading-api", "ClusterName", "${ECSClusterName}" ],
                  [ ".", "MemoryUtilization", ".", ".", ".", "." ],
                  [ ".", "CPUUtilization", "ServiceName", "${ProjectName}-${Environment}-risk-engine", "ClusterName", "${ECSClusterName}" ],
                  [ ".", "MemoryUtilization", ".", ".", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "ECS Service Metrics",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 6,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/RDS", "CPUUtilization", "DBClusterIdentifier", "${DatabaseClusterIdentifier}" ],
                  [ ".", "DatabaseConnections", ".", "." ],
                  [ ".", "ReadLatency", ".", "." ],
                  [ ".", "WriteLatency", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Aurora PostgreSQL Metrics",
                "period": 300
              }
            },
            {
              "type": "log",
              "x": 12,
              "y": 6,
              "width": 12,
              "height": 6,
              "properties": {
                "query": "SOURCE '/aws/ecs/${ProjectName}-${Environment}/trading-api'\n| fields @timestamp, @message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 100",
                "region": "${AWS::Region}",
                "title": "Recent Errors from Trading API",
                "view": "table"
              }
            }
          ]
        }

  # Custom Metrics for Trading Platform
  TradingAPIResponseTimeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-trading-api-response-time'
      AlarmDescription: Trading API response time is too high
      MetricName: TargetResponseTime
      Namespace: AWS/ApplicationELB
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 15200  # 95% of max_connections (16000)
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBClusterIdentifier
          Value: !Ref DatabaseClusterIdentifier
      AlarmActions:
        - !Ref CriticalAlertsTopic
      OKActions:
        - !Ref CriticalAlertsTopic
      TreatMissingData: notBreaching

  DatabaseReadLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-database-read-latency-high'
      AlarmDescription: Database read latency is too high
      MetricName: ReadLatency
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.05  # 50ms
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBClusterIdentifier
          Value: !Ref DatabaseClusterIdentifier
      AlarmActions:
        - !Ref WarningAlertsTopic
      OKActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  DatabaseWriteLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-database-write-latency-high'
      AlarmDescription: Database write latency is too high
      MetricName: WriteLatency
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.05  # 50ms
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBClusterIdentifier
          Value: !Ref DatabaseClusterIdentifier
      AlarmActions:
        - !Ref WarningAlertsTopic
      OKActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  # Custom CloudWatch Metrics for Trading Platform
  TradingMetricsLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: CloudWatchMetricsPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - cloudwatch:PutMetricData
                  - rds:DescribeDBClusters
                  - rds:DescribeDBInstances
                  - ecs:DescribeServices
                  - ecs:DescribeTasks
                Resource: '*'
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ProjectName}-${Environment}/*'

  TradingMetricsLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub '${ProjectName}-${Environment}-trading-metrics'
      Runtime: python3.11
      Handler: index.lambda_handler
      Role: !GetAtt TradingMetricsLambdaRole.Arn
      Timeout: 60
      ReservedConcurrentExecutions: 10
      DeadLetterConfig:
        TargetArn: !GetAtt TradingMetricsDLQ.Arn
      Environment:
        Variables:
          PROJECT_NAME: !Ref ProjectName
          ENVIRONMENT: !Ref Environment
          DB_CLUSTER_IDENTIFIER: !Ref DatabaseClusterIdentifier
          ECS_CLUSTER_NAME: !Ref ECSClusterName
      KmsKeyArn: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-secrets'
      Code:
        ZipFile: |
          import json
          import boto3
          import os
          from datetime import datetime, timezone
          
          def lambda_handler(event, context):
              cloudwatch = boto3.client('cloudwatch')
              rds = boto3.client('rds')
              ecs = boto3.client('ecs')
              
              project_name = os.environ['PROJECT_NAME']
              environment = os.environ['ENVIRONMENT']
              
              try:
                  # Get database metrics
                  db_response = rds.describe_db_clusters(
                      DBClusterIdentifier=os.environ['DB_CLUSTER_IDENTIFIER']
                  )
                  
                  cluster = db_response['DBClusters'][0]
                  
                  # Custom metrics for database
                  cloudwatch.put_metric_data(
                      Namespace=f'{project_name}/Database',
                      MetricData=[
                          {
                              'MetricName': 'ClusterStatus',
                              'Value': 1 if cluster['Status'] == 'available' else 0,
                              'Unit': 'Count',
                              'Dimensions': [
                                  {
                                      'Name': 'Environment',
                                      'Value': environment
                                  },
                                  {
                                      'Name': 'ClusterIdentifier',
                                      'Value': cluster['DBClusterIdentifier']
                                  }
                              ]
                          }
                      ]
                  )
                  
                  # Get ECS service metrics
                  services_response = ecs.describe_services(
                      cluster=os.environ['ECS_CLUSTER_NAME'],
                      services=[
                          f'{project_name}-{environment}-trading-api',
                          f'{project_name}-{environment}-risk-engine',
                          f'{project_name}-{environment}-fraud-detection',
                          f'{project_name}-{environment}-market-data'
                      ]
                  )
                  
                  for service in services_response['services']:
                      service_name = service['serviceName']
                      desired_count = service['desiredCount']
                      running_count = service['runningCount']
                      pending_count = service['pendingCount']
                      
                      cloudwatch.put_metric_data(
                          Namespace=f'{project_name}/ECS',
                          MetricData=[
                              {
                                  'MetricName': 'ServiceHealth',
                                  'Value': 1 if running_count >= desired_count else 0,
                                  'Unit': 'Count',
                                  'Dimensions': [
                                      {
                                          'Name': 'Environment',
                                          'Value': environment
                                      },
                                      {
                                          'Name': 'ServiceName',
                                          'Value': service_name
                                      }
                                  ]
                              },
                              {
                                  'MetricName': 'TasksRunning',
                                  'Value': running_count,
                                  'Unit': 'Count',
                                  'Dimensions': [
                                      {
                                          'Name': 'Environment',
                                          'Value': environment
                                      },
                                      {
                                          'Name': 'ServiceName',
                                          'Value': service_name
                                      }
                                  ]
                              },
                              {
                                  'MetricName': 'TasksPending',
                                  'Value': pending_count,
                                  'Unit': 'Count',
                                  'Dimensions': [
                                      {
                                          'Name': 'Environment',
                                          'Value': environment
                                      },
                                      {
                                          'Name': 'ServiceName',
                                          'Value': service_name
                                      }
                                  ]
                              }
                          ]
                      )
                  
                  return {
                      'statusCode': 200,
                      'body': json.dumps('Metrics published successfully')
                  }
                  
              except Exception as e:
                  print(f"Error: {str(e)}")
                  return {
                      'statusCode': 500,
                      'body': json.dumps(f'Error: {str(e)}')
                  }

  # EventBridge rule to trigger metrics collection
  TradingMetricsSchedule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-metrics-schedule'
      Description: Schedule for collecting trading platform metrics
      ScheduleExpression: 'rate(5 minutes)'
      State: ENABLED
      Targets:
        - Arn: !GetAtt TradingMetricsLambda.Arn
          Id: TradingMetricsTarget

  TradingMetricsDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub '${ProjectName}-${Environment}-trading-metrics-dlq'
      KmsMasterKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-secrets'
      MessageRetentionPeriod: 1209600

  TradingMetricsLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref TradingMetricsLambda
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt TradingMetricsSchedule.Arn

  # X-Ray Tracing for microservices
  XRayTracingConfigRule:
    Type: AWS::Config::ConfigRule
    Properties:
      ConfigRuleName: !Sub '${ProjectName}-${Environment}-xray-tracing-enabled'
      Description: Checks whether X-Ray tracing is enabled for ECS services
      Source:
        Owner: AWS
        SourceIdentifier: ECS_SERVICE_XRAY_TRACING_ENABLED
      Scope:
        ComplianceResourceTypes:
          - AWS::ECS::Service

  # Performance Insights for database monitoring
  DatabasePerformanceInsightsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-database-performance-insights'
      AlarmDescription: Database performance insights showing high load
      MetricName: DBLoad
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 3
      Threshold: 80
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Sub '${ProjectName}-${Environment}-aurora-primary'
      AlarmActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  # Log Insights queries for error detection
  ErrorDetectionLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/${ProjectName}/${Environment}/error-detection'
      RetentionInDays: 7
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'

  # Composite alarm for overall system health
  SystemHealthCompositeAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-system-health'
      AlarmDescription: Overall system health based on multiple metrics
      AlarmRule: !Sub |
        ALARM(${TradingAPIResponseTimeAlarm}) OR
        ALARM(${DatabaseReadLatencyAlarm}) OR
        ALARM(${DatabaseWriteLatencyAlarm}) OR
        ALARM(${ALBHighRequestCountAlarm})
      ActionsEnabled: true
      AlarmActions:
        - !Ref CriticalAlertsTopic
      OKActions:
        - !Ref CriticalAlertsTopic

  # Additional ECS Service Alarms for other microservices
  RiskEngineResponseTimeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-risk-engine-response-time'
      AlarmDescription: Risk Engine response time is too high
      MetricName: TargetResponseTime
      Namespace: AWS/ApplicationELB
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.05  # 50ms for risk calculations
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: !Ref ApplicationLoadBalancerFullName
        - Name: TargetGroup
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-tg'
      AlarmActions:
        - !Ref CriticalAlertsTopic
      OKActions:
        - !Ref CriticalAlertsTopic
      TreatMissingData: notBreaching

  FraudDetectionResponseTimeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-fraud-detection-response-time'
      AlarmDescription: Fraud Detection response time is too high
      MetricName: TargetResponseTime
      Namespace: AWS/ApplicationELB
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.1  # 100ms for fraud scoring
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: !Ref ApplicationLoadBalancerFullName
        - Name: TargetGroup
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-tg'
      AlarmActions:
        - !Ref WarningAlertsTopic
      OKActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  # ALB Request Count Alarm for high traffic detection
  ALBHighRequestCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-alb-high-request-count'
      AlarmDescription: High request count detected on ALB
      MetricName: RequestCount
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 50000  # 10,000 requests per minute
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: !Ref ApplicationLoadBalancerFullName
      AlarmActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  # Database specific alarms
  DatabaseFreeStorageSpaceAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-database-storage-low'
      AlarmDescription: Database free storage space is low
      MetricName: FreeStorageSpace
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 21474836480  # 20 GB in bytes
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: DBClusterIdentifier
          Value: !Ref DatabaseClusterIdentifier
      AlarmActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  DatabaseSwapUsageAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-database-swap-usage'
      AlarmDescription: Database swap usage is high
      MetricName: SwapUsage
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 268435456  # 256 MB in bytes
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Sub '${ProjectName}-${Environment}-aurora-primary'
      AlarmActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  # ECS Task Health Monitoring
  ECSTaskCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-ecs-task-count-low'
      AlarmDescription: ECS running task count is below desired
      MetricName: RunningTaskCount
      Namespace: ECS/ContainerInsights
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 3  # Minimum healthy tasks
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: ServiceName
          Value: !Sub '${ProjectName}-${Environment}-trading-api'
        - Name: ClusterName
          Value: !Ref ECSClusterName
      AlarmActions:
        - !Ref CriticalAlertsTopic
      TreatMissingData: breaching

  # Custom Log-based Alarms
  ApplicationErrorLogAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-application-errors'
      AlarmDescription: High number of application errors detected in logs
      MetricName: ErrorCount
      Namespace: !Sub '${ProjectName}/Application'
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref CriticalAlertsTopic
      TreatMissingData: notBreaching

  # Metric Filter for Error Detection
  ApplicationErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/trading-api'
      FilterPattern: '[timestamp, request_id, level="ERROR", ...]'
      MetricTransformations:
        - MetricNamespace: !Sub '${ProjectName}/Application'
          MetricName: ErrorCount
          MetricValue: '1'
          DefaultValue: 0

  DatabaseErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub '/aws/rds/cluster/${DatabaseClusterIdentifier}/postgresql'
      FilterPattern: '[timestamp, level="ERROR" || level="FATAL", ...]'
      MetricTransformations:
        - MetricNamespace: !Sub '${ProjectName}/Database'
          MetricName: DatabaseErrorCount
          MetricValue: '1'
          DefaultValue: 0

  # Business Logic Alarms (Custom Metrics)
  TradingVolumeAnomalyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-trading-volume-anomaly'
      AlarmDescription: Trading volume anomaly detected
      MetricName: TransactionVolume
      Namespace: !Sub '${ProjectName}/Business'
      Statistic: Sum
      Period: 900  # 15 minutes
      EvaluationPeriods: 2
      Threshold: 100000  # Transactions per 15 minutes
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref WarningAlertsTopic
      TreatMissingData: notBreaching

  # Enhanced monitoring for compliance
  ComplianceMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub '/aws/rds/cluster/${DatabaseClusterIdentifier}/postgresql'
      FilterPattern: '[timestamp, level, message*="AUDIT", ...]'
      MetricTransformations:
        - MetricNamespace: !Sub '${ProjectName}/Compliance'
          MetricName: AuditLogCount
          MetricValue: '1'
          DefaultValue: 0

  SecurityEventMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/trading-api'
      FilterPattern: '[timestamp, request_id, level, message*="SECURITY" || message*="UNAUTHORIZED", ...]'
      MetricTransformations:
        - MetricNamespace: !Sub '${ProjectName}/Security'
          MetricName: SecurityEventCount
          MetricValue: '1'
          DefaultValue: 0

  SecurityEventAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-security-events'
      AlarmDescription: Security events detected in application logs
      MetricName: SecurityEventCount
      Namespace: !Sub '${ProjectName}/Security'
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref CriticalAlertsTopic
      TreatMissingData: notBreaching

  # Performance monitoring for sub-100ms requirement
  PerformanceComplianceAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ProjectName}-${Environment}-performance-sla-breach'
      AlarmDescription: Performance SLA breach - response time >100ms for 95th percentile
      MetricName: TargetResponseTime
      Namespace: AWS/ApplicationELB
      Period: 300
      EvaluationPeriods: 3
      Threshold: 0.1  # 100ms
      ComparisonOperator: GreaterThanThreshold
      ExtendedStatistic: p95
      Dimensions:
        - Name: LoadBalancer
          Value: !Ref ApplicationLoadBalancerFullName
      AlarmActions:
        - !Ref CriticalAlertsTopic
      TreatMissingData: notBreaching

  # Network-level monitoring
  VPCFlowLogsDeliveryRoleForCloudWatchLogs:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: vpc-flow-logs.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: CloudWatchLogsDeliveryRolePolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogGroups
                  - logs:DescribeLogStreams
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/vpc/${ProjectName}-${Environment}/*'

  VPCFlowLogsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/vpc/${ProjectName}-${Environment}/flowlogs'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'

  # Synthetics for end-to-end monitoring
  SyntheticsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: SyntheticsPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                Resource:
                  - !Sub 'arn:aws:s3:::${ProjectName}-${Environment}-synthetics-${AWS::AccountId}/*'
              - Effect: Allow
                Action:
                  - cloudwatch:PutMetricData
                Resource:
                  - !Sub 'arn:aws:cloudwatch:${AWS::Region}:${AWS::AccountId}:metric/AWS/Synthetics/*'
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${ProjectName}-${Environment}-*'

  TradingAPIHealthCheckCanary:
    Type: AWS::Synthetics::Canary
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-trading-api-health'
      ArtifactS3Location: !Sub 's3://${ProjectName}-${Environment}-synthetics-${AWS::AccountId}/canary-artifacts'
      ExecutionRoleArn: !GetAtt SyntheticsRole.Arn
      Schedule:
        Expression: 'rate(5 minutes)'
      RunConfig:
        TimeoutInSeconds: 60
      Code:
        Handler: apiCanaryBlueprint.handler
        Script: !Sub |
          const synthetics = require('Synthetics');
          const log = require('SyntheticsLogger');
          
          const apiCanaryBlueprint = async function () {
              const config = {
                  includeRequestHeaders: true,
                  includeResponseHeaders: true,
                  restrictedHeaders: [],
                  restrictedUrlParameters: []
              };
              
              return await synthetics.executeStep('checkTradingAPIHealth', async function () {
                  return await synthetics.executeHttpStep('GET_health_endpoint', 
                      `https://${ApplicationLoadBalancerFullName}/api/v1/health`, 
                      config);
              });
          };
          
          exports.handler = async () => {
              return await synthetics.executeStep('synthetics', apiCanaryBlueprint);
          };
      RuntimeVersion: syn-nodejs-puppeteer-6.2
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-health-check'
        - Key: Environment
          Value: !Ref Environment

  # S3 bucket for synthetics artifacts
  SyntheticsArtifactsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '${ProjectName}-${Environment}-synthetics-${AWS::AccountId}'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: aws:kms
              KMSMasterKeyID: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-s3'
            BucketKeyEnabled: true
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldArtifacts
            Status: Enabled
            ExpirationInDays: 30
            NoncurrentVersionExpirationInDays: 7
      LoggingConfiguration:
        DestinationBucketName: !Ref SyntheticsAccessLogsBucket
        LogFilePrefix: synthetics-access-logs/
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-synthetics-bucket'
        - Key: Environment
          Value: !Ref Environment

  # S3 bucket for synthetics access logs
  SyntheticsAccessLogsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '${ProjectName}-${Environment}-synthetics-access-logs-${AWS::AccountId}'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: aws:kms
              KMSMasterKeyID: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-s3'
            BucketKeyEnabled: true
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldAccessLogs
            Status: Enabled
            ExpirationInDays: 90
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-synthetics-access-logs-bucket'
        - Key: Environment
          Value: !Ref Environment

Outputs:
  DashboardURL:
    Description: CloudWatch Dashboard URL
    Value: !Sub 'https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${ProjectName}-${Environment}-dashboard'
    Export:
      Name: !Sub '${AWS::StackName}-DashboardURL'

  CriticalAlertsTopicArn:
    Description: Critical Alerts SNS Topic ARN
    Value: !Ref CriticalAlertsTopic
    Export:
      Name: !Sub '${AWS::StackName}-CriticalAlertsTopicArn'

  WarningAlertsTopicArn:
    Description: Warning Alerts SNS Topic ARN
    Value: !Ref WarningAlertsTopic
    Export:
      Name: !Sub '${AWS::StackName}-WarningAlertsTopicArn'

  TradingMetricsLambdaArn:
    Description: Trading Metrics Lambda Function ARN
    Value: !GetAtt TradingMetricsLambda.Arn
    Export:
      Name: !Sub '${AWS::StackName}-TradingMetricsLambdaArn'

  SystemHealthAlarmArn:
    Description: System Health Composite Alarm ARN
    Value: !GetAtt SystemHealthCompositeAlarm.Arn
    Export:
      Name: !Sub '${AWS::StackName}-SystemHealthAlarmArn'
