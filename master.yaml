AWSTemplateFormatVersion: '2010-09-09'
Description: 'Master template for Global Investment Bank Trading Platform - Containerized Microservices with ECS and Aurora PostgreSQL'

Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      - Label:
          default: "Environment Configuration"
        Parameters:
          - Environment
          - ProjectName
          - Owner
      - Label:
          default: "Network Configuration"
        Parameters:
          - VpcCIDR
          - PublicSubnet1CIDR
          - PublicSubnet2CIDR
          - PublicSubnet3CIDR
          - PrivateSubnet1CIDR
          - PrivateSubnet2CIDR
          - PrivateSubnet3CIDR
          - DatabaseSubnet1CIDR
          - DatabaseSubnet2CIDR
          - DatabaseSubnet3CIDR
      - Label:
          default: "Security Configuration"
        Parameters:
          - TrustedCIDR
          - DeletionProtection
          - BackupRetentionPeriod
          - CertificateArn
      - Label:
          default: "Database Configuration"
        Parameters:
          - DatabaseInstanceClass
          - DatabaseReaderInstanceClass
          - DatabaseEngine
          - DatabaseUsername
          - DatabaseName
          - EnablePerformanceInsights
      - Label:
          default: "Container Configuration"
        Parameters:
          - ECSClusterName
          - ContainerImageRepository
          - ContainerImageTag
          - TradingApiTaskCpu
          - TradingApiTaskMemory
          - TradingApiMinCapacity
          - TradingApiMaxCapacity
      - Label:
          default: "Template URLs"
        Parameters:
          - TemplateS3Bucket
          - NetworkTemplateKey
          - SecurityTemplateKey
          - DatabaseTemplateKey
          - ECSTemplateKey
          - MonitoringTemplateKey
          - LoadBalancerTemplateKey

Parameters:
  Environment:
    Type: String
    Default: 'production'
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: 'trading-platform'
    Description: Project name for resource naming

  Owner:
    Type: String
    Default: 'platform-team'
    Description: Team or individual responsible for this stack

  VpcCIDR:
    Type: String
    Default: '10.0.0.0/16'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for VPC

  PublicSubnet1CIDR:
    Type: String
    Default: '********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for public subnet in AZ1

  PublicSubnet2CIDR:
    Type: String
    Default: '********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for public subnet in AZ2

  PublicSubnet3CIDR:
    Type: String
    Default: '********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for public subnet in AZ3

  PrivateSubnet1CIDR:
    Type: String
    Default: '*********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for private subnet in AZ1

  PrivateSubnet2CIDR:
    Type: String
    Default: '*********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for private subnet in AZ2

  PrivateSubnet3CIDR:
    Type: String
    Default: '*********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for private subnet in AZ3

  DatabaseSubnet1CIDR:
    Type: String
    Default: '*********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for database subnet in AZ1

  DatabaseSubnet2CIDR:
    Type: String
    Default: '*********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for database subnet in AZ2

  DatabaseSubnet3CIDR:
    Type: String
    Default: '*********/24'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block for database subnet in AZ3

  TrustedCIDR:
    Type: String
    Default: '10.0.0.0/16'
    AllowedPattern: '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/(1[6-9]|2[0-8]))$'
    Description: CIDR block that should be allowed access to the environment

  # FIXED: Added CertificateArn parameter
  CertificateArn:
    Type: String
    Default: ''
    Description: ACM Certificate ARN for HTTPS listener (optional)

  DeletionProtection:
    Type: String
    Default: 'true'
    AllowedValues:
      - 'true'
      - 'false'
    Description: Enable deletion protection for critical resources

  BackupRetentionPeriod:
    Type: Number
    Default: 35
    MinValue: 7
    MaxValue: 35
    Description: Number of days to retain database backups

  DatabaseInstanceClass:
    Type: String
    Default: 'db.r6g.2xlarge'
    AllowedValues:
      - db.r6g.large
      - db.r6g.xlarge
      - db.r6g.2xlarge
      - db.r6g.4xlarge
      - db.r6g.8xlarge
      - db.r6g.12xlarge
      - db.r6g.16xlarge
    Description: Database instance class for primary instance

  DatabaseReaderInstanceClass:
    Type: String
    Default: 'db.r6g.xlarge'
    AllowedValues:
      - db.r6g.large
      - db.r6g.xlarge
      - db.r6g.2xlarge
      - db.r6g.4xlarge
      - db.r6g.8xlarge
      - db.r6g.12xlarge
      - db.r6g.16xlarge
    Description: Database instance class for reader instances

  DatabaseEngine:
    Type: String
    Default: 'aurora-postgresql'
    AllowedValues:
      - aurora-postgresql
    Description: Database engine

  DatabaseUsername:
    Type: String
    Default: 'trading_admin'
    AllowedPattern: '^[a-zA-Z][a-zA-Z0-9_]*$'
    ConstraintDescription: Must begin with a letter and contain only alphanumeric characters and underscores
    Description: Database master username

  DatabaseName:
    Type: String
    Default: 'trading_db'
    AllowedPattern: '^[a-zA-Z][a-zA-Z0-9_]*$'
    ConstraintDescription: Must begin with a letter and contain only alphanumeric characters and underscores
    Description: Database name

  EnablePerformanceInsights:
    Type: String
    Default: 'true'
    AllowedValues:
      - 'true'
      - 'false'
    Description: Enable Performance Insights

  ECSClusterName:
    Type: String
    Default: 'trading-cluster'
    Description: Name for the ECS cluster

  ContainerImageRepository:
    Type: String
    Default: 'public.ecr.aws/nginx/nginx'
    Description: ECR repository base URL (default uses public nginx for testing)

  ContainerImageTag:
    Type: String
    Default: 'latest'
    Description: Container image tag

  TradingApiTaskCpu:
    Type: Number
    Default: 4096
    AllowedValues: [256, 512, 1024, 2048, 4096, 8192, 16384]
    Description: Task CPU units for Trading API (1 vCPU = 1024 units)

  TradingApiTaskMemory:
    Type: Number
    Default: 8192
    Description: Task memory in MB for Trading API

  TradingApiMinCapacity:
    Type: Number
    Default: 4
    MinValue: 1
    MaxValue: 50
    Description: Minimum number of Trading API tasks

  TradingApiMaxCapacity:
    Type: Number
    Default: 20
    MinValue: 1
    MaxValue: 100
    Description: Maximum number of Trading API tasks

  TemplateS3Bucket:
    Type: String
    Description: S3 bucket containing nested CloudFormation templates

  NetworkTemplateKey:
    Type: String
    Default: 'templates/network.yaml'
    Description: S3 key for network template

  SecurityTemplateKey:
    Type: String
    Default: 'templates/security.yaml'
    Description: S3 key for security template

  DatabaseTemplateKey:
    Type: String
    Default: 'templates/database.yaml'
    Description: S3 key for database template

  ECSTemplateKey:
    Type: String
    Default: 'templates/ecs.yaml'
    Description: S3 key for ECS template

  MonitoringTemplateKey:
    Type: String
    Default: 'templates/monitoring.yaml'
    Description: S3 key for monitoring template

  LoadBalancerTemplateKey:
    Type: String
    Default: 'templates/loadbalancer.yaml'
    Description: S3 key for load balancer template

Resources:
  # Network Infrastructure Stack
  NetworkStack:
    Type: AWS::CloudFormation::Stack
    Properties:
      TemplateURL: !Sub 'https://${TemplateS3Bucket}.s3.amazonaws.com/${NetworkTemplateKey}'
      Parameters:
        Environment: !Ref Environment
        ProjectName: !Ref ProjectName
        VpcCIDR: !Ref VpcCIDR
        PublicSubnet1CIDR: !Ref PublicSubnet1CIDR
        PublicSubnet2CIDR: !Ref PublicSubnet2CIDR
        PublicSubnet3CIDR: !Ref PublicSubnet3CIDR
        PrivateSubnet1CIDR: !Ref PrivateSubnet1CIDR
        PrivateSubnet2CIDR: !Ref PrivateSubnet2CIDR
        PrivateSubnet3CIDR: !Ref PrivateSubnet3CIDR
        DatabaseSubnet1CIDR: !Ref DatabaseSubnet1CIDR
        DatabaseSubnet2CIDR: !Ref DatabaseSubnet2CIDR
        DatabaseSubnet3CIDR: !Ref DatabaseSubnet3CIDR
        AvailabilityZoneCount: 3
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-network'
        - Key: Environment
          Value: !Ref Environment
        - Key: Owner
          Value: !Ref Owner

  # Security Stack
  SecurityStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: NetworkStack
    Properties:
      TemplateURL: !Sub 'https://${TemplateS3Bucket}.s3.amazonaws.com/${SecurityTemplateKey}'
      Parameters:
        Environment: !Ref Environment
        ProjectName: !Ref ProjectName
        VpcId: !GetAtt NetworkStack.Outputs.VpcId
        TrustedCIDR: !Ref TrustedCIDR
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-security'
        - Key: Environment
          Value: !Ref Environment
        - Key: Owner
          Value: !Ref Owner

  # Load Balancer Stack
  LoadBalancerStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: SecurityStack
    Properties:
      TemplateURL: !Sub 'https://${TemplateS3Bucket}.s3.amazonaws.com/${LoadBalancerTemplateKey}'
      Parameters:
        Environment: !Ref Environment
        ProjectName: !Ref ProjectName
        VpcId: !GetAtt NetworkStack.Outputs.VpcId
        PublicSubnet1Id: !GetAtt NetworkStack.Outputs.PublicSubnet1Id
        PublicSubnet2Id: !GetAtt NetworkStack.Outputs.PublicSubnet2Id
        PublicSubnet3Id: !GetAtt NetworkStack.Outputs.PublicSubnet3Id
        ALBSecurityGroupId: !GetAtt SecurityStack.Outputs.ALBSecurityGroupId
        CertificateArn: !Ref CertificateArn
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-alb'
        - Key: Environment
          Value: !Ref Environment
        - Key: Owner
          Value: !Ref Owner

  # Database Stack
  DatabaseStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: SecurityStack
    Properties:
      TemplateURL: !Sub 'https://${TemplateS3Bucket}.s3.amazonaws.com/${DatabaseTemplateKey}'
      Parameters:
        Environment: !Ref Environment
        ProjectName: !Ref ProjectName
        DatabaseSubnet1Id: !GetAtt NetworkStack.Outputs.DatabaseSubnet1Id
        DatabaseSubnet2Id: !GetAtt NetworkStack.Outputs.DatabaseSubnet2Id
        DatabaseSubnet3Id: !GetAtt NetworkStack.Outputs.DatabaseSubnet3Id
        DatabaseSecurityGroupId: !GetAtt SecurityStack.Outputs.DatabaseSecurityGroupId
        DatabaseInstanceClass: !Ref DatabaseInstanceClass
        DatabaseReaderInstanceClass: !Ref DatabaseReaderInstanceClass
        DatabaseEngine: !Ref DatabaseEngine
        DatabaseUsername: !Ref DatabaseUsername
        DatabaseName: !Ref DatabaseName
        DeletionProtection: !Ref DeletionProtection
        BackupRetentionPeriod: !Ref BackupRetentionPeriod
        EnablePerformanceInsights: !Ref EnablePerformanceInsights
        KMSKeyId: !GetAtt SecurityStack.Outputs.DatabaseKMSKeyId
        RDSProxyRoleArn: !GetAtt SecurityStack.Outputs.RDSProxyRoleArn
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-database'
        - Key: Environment
          Value: !Ref Environment
        - Key: Owner
          Value: !Ref Owner

  # ECS Stack - FIXED: Added target group ARN parameters
  ECSStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: 
      - DatabaseStack
      - LoadBalancerStack
    Properties:
      TemplateURL: !Sub 'https://${TemplateS3Bucket}.s3.amazonaws.com/${ECSTemplateKey}'
      Parameters:
        Environment: !Ref Environment
        ProjectName: !Ref ProjectName
        VpcId: !GetAtt NetworkStack.Outputs.VpcId
        PrivateSubnet1Id: !GetAtt NetworkStack.Outputs.PrivateSubnet1Id
        PrivateSubnet2Id: !GetAtt NetworkStack.Outputs.PrivateSubnet2Id
        PrivateSubnet3Id: !GetAtt NetworkStack.Outputs.PrivateSubnet3Id
        ECSSecurityGroupId: !GetAtt SecurityStack.Outputs.ECSSecurityGroupId
        ECSClusterName: !Ref ECSClusterName
        ContainerImageRepository: !Ref ContainerImageRepository
        ContainerImageTag: !Ref ContainerImageTag
        TradingApiTaskCpu: !Ref TradingApiTaskCpu
        TradingApiTaskMemory: !Ref TradingApiTaskMemory
        TradingApiMinCapacity: !Ref TradingApiMinCapacity
        TradingApiMaxCapacity: !Ref TradingApiMaxCapacity
        DatabaseProxyEndpoint: !GetAtt DatabaseStack.Outputs.DatabaseProxyEndpoint
        SecretsManagerArn: !GetAtt DatabaseStack.Outputs.DatabaseSecretArn
        # FIXED: Pass target group ARNs from LoadBalancerStack
        TradingAPITargetGroupArn: !GetAtt LoadBalancerStack.Outputs.TradingAPITargetGroupArn
        RiskEngineTargetGroupArn: !GetAtt LoadBalancerStack.Outputs.RiskEngineTargetGroupArn
        FraudDetectionTargetGroupArn: !GetAtt LoadBalancerStack.Outputs.FraudDetectionTargetGroupArn
        MarketDataTargetGroupArn: !GetAtt LoadBalancerStack.Outputs.MarketDataTargetGroupArn
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-ecs'
        - Key: Environment
          Value: !Ref Environment
        - Key: Owner
          Value: !Ref Owner

  # Monitoring Stack
  MonitoringStack:
    Type: AWS::CloudFormation::Stack
    DependsOn: ECSStack
    Properties:
      TemplateURL: !Sub 'https://${TemplateS3Bucket}.s3.amazonaws.com/${MonitoringTemplateKey}'
      Parameters:
        Environment: !Ref Environment
        ProjectName: !Ref ProjectName
        ECSClusterName: !Ref ECSClusterName
        DatabaseClusterIdentifier: !GetAtt DatabaseStack.Outputs.DatabaseClusterIdentifier
        ApplicationLoadBalancerFullName: !GetAtt LoadBalancerStack.Outputs.ApplicationLoadBalancerFullName
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-monitoring'
        - Key: Environment
          Value: !Ref Environment
        - Key: Owner
          Value: !Ref Owner

Outputs:
  VpcId:
    Description: VPC ID
    Value: !GetAtt NetworkStack.Outputs.VpcId
    Export:
      Name: !Sub '${AWS::StackName}-VpcId'

  DatabaseClusterEndpoint:
    Description: Aurora PostgreSQL cluster endpoint
    Value: !GetAtt DatabaseStack.Outputs.DatabaseClusterEndpoint
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseClusterEndpoint'

  DatabaseProxyEndpoint:
    Description: RDS Proxy endpoint
    Value: !GetAtt DatabaseStack.Outputs.DatabaseProxyEndpoint
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseProxyEndpoint'

  ApplicationLoadBalancerDNS:
    Description: Application Load Balancer DNS name
    Value: !GetAtt LoadBalancerStack.Outputs.ApplicationLoadBalancerDNS
    Export:
      Name: !Sub '${AWS::StackName}-ApplicationLoadBalancerDNS'

  ECSClusterName:
    Description: ECS cluster name
    Value: !GetAtt ECSStack.Outputs.ECSClusterName
    Export:
      Name: !Sub '${AWS::StackName}-ECSClusterName'

  DatabaseSecretArn:
    Description: Database credentials secret ARN
    Value: !GetAtt DatabaseStack.Outputs.DatabaseSecretArn
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseSecretArn'

  # FIXED: Added additional useful outputs
  TradingAPIServiceArn:
    Description: Trading API ECS Service ARN
    Value: !GetAtt ECSStack.Outputs.TradingAPIServiceArn
    Export:
      Name: !Sub '${AWS::StackName}-TradingAPIServiceArn'

  RiskEngineServiceArn:
    Description: Risk Engine ECS Service ARN
    Value: !GetAtt ECSStack.Outputs.RiskEngineServiceArn
    Export:
      Name: !Sub '${AWS::StackName}-RiskEngineServiceArn'

  FraudDetectionServiceArn:
    Description: Fraud Detection ECS Service ARN
    Value: !GetAtt ECSStack.Outputs.FraudDetectionServiceArn
    Export:
      Name: !Sub '${AWS::StackName}-FraudDetectionServiceArn'

  MarketDataServiceArn:
    Description: Market Data ECS Service ARN
    Value: !GetAtt ECSStack.Outputs.MarketDataServiceArn
    Export:
      Name: !Sub '${AWS::StackName}-MarketDataServiceArn'

  MonitoringDashboardURL:
    Description: CloudWatch Dashboard URL
    Value: !GetAtt MonitoringStack.Outputs.DashboardURL
    Export:
      Name: !Sub '${AWS::StackName}-MonitoringDashboardURL'

  CriticalAlertsTopicArn:
    Description: Critical Alerts SNS Topic ARN
    Value: !GetAtt MonitoringStack.Outputs.CriticalAlertsTopicArn
    Export:
      Name: !Sub '${AWS::StackName}-CriticalAlertsTopicArn'

  # Security Information
  WebACLArn:
    Description: WAF Web ACL ARN
    Value: !GetAtt SecurityStack.Outputs.WebACLArn
    Export:
      Name: !Sub '${AWS::StackName}-WebACLArn'

  CloudTrailBucketName:
    Description: CloudTrail S3 Bucket Name
    Value: !GetAtt SecurityStack.Outputs.CloudTrailBucketName
    Export:
      Name: !Sub '${AWS::StackName}-CloudTrailBucketName'