AWSTemplateFormatVersion: '2010-09-09'
Description: 'Security infrastructure for Trading Platform - Security Groups, KMS Keys, IAM Roles'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ProjectName:
    Type: String
    Description: Project name for resource naming

  VpcId:
    Type: String
    Description: VPC ID where security groups will be created

  TrustedCIDR:
    Type: String
    Description: CIDR block that should be allowed access to the environment

Resources:
  # KMS Keys
  DatabaseKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub 'KMS key for ${ProjectName} ${Environment} Aurora PostgreSQL encryption'
      EnableKeyRotation: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${AWS::AccountId}:root'
            Action: 'kms:*'
            Resource: '*'
          - Sid: Allow RDS Service
            Effect: Allow
            Principal:
              Service: rds.amazonaws.com
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
              - kms:CreateGrant
            Resource: '*'
          - Sid: Allow RDS Proxy Service
            Effect: Allow
            Principal:
              Service: rds.amazonaws.com
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
            Resource: '*'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-database-kms'
        - Key: Environment
          Value: !Ref Environment

  DatabaseKMSKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${ProjectName}-${Environment}-database'
      TargetKeyId: !Ref DatabaseKMSKey

  SecretsManagerKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub 'KMS key for ${ProjectName} ${Environment} Secrets Manager encryption'
      EnableKeyRotation: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${AWS::AccountId}:root'
            Action: 'kms:*'
            Resource: '*'
          - Sid: Allow Secrets Manager Service
            Effect: Allow
            Principal:
              Service: secretsmanager.amazonaws.com
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
            Resource: '*'

      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-secrets-kms'
        - Key: Environment
          Value: !Ref Environment

  SecretsManagerKMSKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${ProjectName}-${Environment}-secrets'
      TargetKeyId: !Ref SecretsManagerKMSKey

  CloudWatchLogsKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub 'KMS key for ${ProjectName} ${Environment} CloudWatch Logs encryption'
      EnableKeyRotation: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${AWS::AccountId}:root'
            Action: 'kms:*'
            Resource: '*'
          - Sid: Allow CloudWatch Logs
            Effect: Allow
            Principal:
              Service: !Sub 'logs.${AWS::Region}.amazonaws.com'
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
            Resource: '*'
            Condition:
              ArnEquals:
                'kms:EncryptionContext:aws:logs:arn': !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/ecs/${ProjectName}-${Environment}/*'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-logs-kms'
        - Key: Environment
          Value: !Ref Environment

  CloudWatchLogsKMSKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${ProjectName}-${Environment}-logs'
      TargetKeyId: !Ref CloudWatchLogsKMSKey

  S3KMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub 'KMS key for ${ProjectName} ${Environment} S3 encryption'
      EnableKeyRotation: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${AWS::AccountId}:root'
            Action: 'kms:*'
            Resource: '*'
          - Sid: Allow S3 Service
            Effect: Allow
            Principal:
              Service: s3.amazonaws.com
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
            Resource: '*'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-s3-kms'
        - Key: Environment
          Value: !Ref Environment

  S3KMSKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub 'alias/${ProjectName}-${Environment}-s3'
      TargetKeyId: !Ref S3KMSKey

  # Security Groups
  ALBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      # Remove GroupName to avoid conflicts - AWS will auto-generate
      GroupDescription: Security group for Application Load Balancer
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: !Ref TrustedCIDR
          Description: Allow HTTPS from trusted networks
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: !Ref TrustedCIDR
          Description: Allow HTTP from trusted networks (redirect to HTTPS)
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: Allow all outbound traffic
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-alb-sg'
        - Key: Environment
          Value: !Ref Environment

  ECSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      # Remove GroupName to avoid conflicts - AWS will auto-generate
      GroupDescription: Security group for ECS tasks
      VpcId: !Ref VpcId
      SecurityGroupIngress: []
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: Allow all outbound traffic


      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-ecs-sg'
        - Key: Environment
          Value: !Ref Environment

  DatabaseSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      # Remove GroupName to avoid conflicts - AWS will auto-generate
      GroupDescription: Security group for Aurora PostgreSQL database
      VpcId: !Ref VpcId
      SecurityGroupIngress: []
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-rds-sg'
        - Key: Environment
          Value: !Ref Environment

  RDSProxySecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      # Remove GroupName to avoid conflicts - AWS will auto-generate
      GroupDescription: Security group for RDS Proxy
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 5432
          ToPort: 5432
          SourceSecurityGroupId: !Ref ECSSecurityGroup
          Description: Allow PostgreSQL from ECS tasks
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: Allow all outbound traffic
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-rds-proxy-sg'
        - Key: Environment
          Value: !Ref Environment

  CacheSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      # Remove GroupName to avoid conflicts - AWS will auto-generate
      GroupDescription: Security group for Redis cache
      VpcId: !Ref VpcId
      SecurityGroupIngress: []
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-cache-sg'
        - Key: Environment
          Value: !Ref Environment

  VPCEndpointSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      # Remove GroupName to avoid conflicts - AWS will auto-generate
      GroupDescription: Security group for VPC endpoints
      VpcId: !Ref VpcId
      SecurityGroupIngress: []
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-vpce-sg'
        - Key: Environment
          Value: !Ref Environment

  # Security Group Rules to break circular dependencies
  ALBToECSIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref ECSSecurityGroup
      IpProtocol: tcp
      FromPort: 8080
      ToPort: 8080
      SourceSecurityGroupId: !Ref ALBSecurityGroup
      Description: Allow traffic from ALB

  ALBToECSEgress:
    Type: AWS::EC2::SecurityGroupEgress
    Properties:
      GroupId: !Ref ALBSecurityGroup
      IpProtocol: tcp
      FromPort: 8080
      ToPort: 8080
      DestinationSecurityGroupId: !Ref ECSSecurityGroup
      Description: Allow traffic to ECS tasks

  ECSToVPCEndpointEgress:
    Type: AWS::EC2::SecurityGroupEgress
    Properties:
      GroupId: !Ref ECSSecurityGroup
      IpProtocol: tcp
      FromPort: 443
      ToPort: 443
      DestinationSecurityGroupId: !Ref VPCEndpointSecurityGroup
      Description: Allow HTTPS to VPC endpoints

  VPCEndpointFromECSIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref VPCEndpointSecurityGroup
      IpProtocol: tcp
      FromPort: 443
      ToPort: 443
      SourceSecurityGroupId: !Ref ECSSecurityGroup
      Description: Allow HTTPS from ECS tasks

  ECSToDBEgress:
    Type: AWS::EC2::SecurityGroupEgress
    Properties:
      GroupId: !Ref ECSSecurityGroup
      IpProtocol: tcp
      FromPort: 5432
      ToPort: 5432
      DestinationSecurityGroupId: !Ref DatabaseSecurityGroup
      Description: Allow PostgreSQL traffic to database

  DatabaseFromECSIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref DatabaseSecurityGroup
      IpProtocol: tcp
      FromPort: 5432
      ToPort: 5432
      SourceSecurityGroupId: !Ref ECSSecurityGroup
      Description: Allow PostgreSQL from ECS tasks

  ECSToRedisEgress:
    Type: AWS::EC2::SecurityGroupEgress
    Properties:
      GroupId: !Ref ECSSecurityGroup
      IpProtocol: tcp
      FromPort: 6379
      ToPort: 6379
      DestinationSecurityGroupId: !Ref CacheSecurityGroup
      Description: Allow Redis traffic to cache

  CacheFromECSIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref CacheSecurityGroup
      IpProtocol: tcp
      FromPort: 6379
      ToPort: 6379
      SourceSecurityGroupId: !Ref ECSSecurityGroup
      Description: Allow Redis from ECS tasks

  RDSProxyToDBEgress:
    Type: AWS::EC2::SecurityGroupEgress
    Properties:
      GroupId: !Ref RDSProxySecurityGroup
      IpProtocol: tcp
      FromPort: 5432
      ToPort: 5432
      DestinationSecurityGroupId: !Ref DatabaseSecurityGroup
      Description: Allow PostgreSQL to Aurora cluster

  DatabaseFromRDSProxyIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref DatabaseSecurityGroup
      IpProtocol: tcp
      FromPort: 5432
      ToPort: 5432
      SourceSecurityGroupId: !Ref RDSProxySecurityGroup
      Description: Allow PostgreSQL from RDS Proxy

  # IAM Roles and Policies
  ECSTaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      # Remove RoleName to avoid conflicts - AWS will auto-generate
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ProjectName}-${Environment}/*'
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                Resource:
                  - !GetAtt SecretsManagerKMSKey.Arn
              - Effect: Allow
                Action:
                  - cloudwatch:PutMetricData
                  - cloudwatch:GetMetricStatistics
                  - cloudwatch:ListMetrics
                Resource: '*'
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/ecs/${ProjectName}-${Environment}/*'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-ecs-task-role'
        - Key: Environment
          Value: !Ref Environment

  RDSProxyRole:
    Type: AWS::IAM::Role
    Properties:
      # Remove RoleName to avoid conflicts - AWS will auto-generate
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: rds.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ProjectName}-${Environment}/*'
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                Resource:
                  - !GetAtt SecretsManagerKMSKey.Arn
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-rds-proxy-role'
        - Key: Environment
          Value: !Ref Environment

  # ECSServiceRole removed - ECS now uses service-linked roles automatically

  AutoScalingRole:
    Type: AWS::IAM::Role
    Properties:
      # Remove RoleName to avoid conflicts - AWS will auto-generate
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: application-autoscaling.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/ApplicationAutoScalingECSServicePolicy
      Policies:
        - PolicyName: ECSServiceScaling
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ecs:DescribeServices
                  - ecs:UpdateService
                Resource:
                  - !Sub 'arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:service/*'
              - Effect: Allow
                Action:
                  - cloudwatch:DescribeAlarms
                  - cloudwatch:PutMetricAlarm
                  - cloudwatch:DeleteAlarms
                Resource:
                  - !Sub 'arn:aws:cloudwatch:${AWS::Region}:${AWS::AccountId}:alarm:*'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-autoscaling-role'
        - Key: Environment
          Value: !Ref Environment

  # SNS Topic for Alerts
  AlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ProjectName}-${Environment}-alerts'
      DisplayName: !Sub 'Trading Platform ${Environment} Alerts'
      KmsMasterKeyId: !Ref SecretsManagerKMSKey
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-alerts-topic'
        - Key: Environment
          Value: !Ref Environment

  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      # Remove RoleName to avoid conflicts - AWS will auto-generate
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: TradingApplicationAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ProjectName}-${Environment}/*'
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                Resource:
                  - !GetAtt SecretsManagerKMSKey.Arn
              - Effect: Allow
                Action:
                  - cloudwatch:PutMetricData
                Resource: '*'
              - Effect: Allow
                Action:
                  - xray:PutTraceSegments
                  - xray:PutTelemetryRecords
                Resource: '*'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-ecs-task-role'
        - Key: Environment
          Value: !Ref Environment

  # WAF for Application Load Balancer
  WebACL:
    Type: AWS::WAFv2::WebACL
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-web-acl'
      Description: WAF rules for Trading Platform
      Scope: REGIONAL
      DefaultAction:
        Allow: {}
      Rules:
        - Name: AWSManagedRulesCommonRuleSet
          Priority: 1
          OverrideAction:
            None: {}
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesCommonRuleSet
              ExcludedRules: []
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: CommonRuleSetMetric
        - Name: AWSManagedRulesKnownBadInputsRuleSet
          Priority: 2
          OverrideAction:
            None: {}
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesKnownBadInputsRuleSet
              ExcludedRules: []
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: KnownBadInputsRuleSetMetric
        - Name: AWSManagedRulesAmazonIpReputationList
          Priority: 3
          OverrideAction:
            None: {}
          Statement:
            ManagedRuleGroupStatement:
              VendorName: AWS
              Name: AWSManagedRulesAmazonIpReputationList
              ExcludedRules: []
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: AmazonIpReputationListMetric
        - Name: RateLimitRule
          Priority: 4
          Action:
            Block: {}
          Statement:
            RateBasedStatement:
              Limit: 10000
              AggregateKeyType: IP
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: RateLimitMetric
      VisibilityConfig:
        SampledRequestsEnabled: true
        CloudWatchMetricsEnabled: true
        MetricName: !Sub '${ProjectName}${Environment}WebACL'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-web-acl'
        - Key: Environment
          Value: !Ref Environment

  # CloudTrail for API auditing
  CloudTrail:
    Type: AWS::CloudTrail::Trail
    DependsOn: CloudTrailBucketPolicy
    Properties:
      # Remove TrailName to avoid conflicts - AWS will auto-generate unique name
      S3BucketName: !Ref CloudTrailBucket
      S3KeyPrefix: !Sub '${ProjectName}-${Environment}'
      IncludeGlobalServiceEvents: true
      IsLogging: true
      IsMultiRegionTrail: true
      EnableLogFileValidation: true
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-cloudtrail'
        - Key: Environment
          Value: !Ref Environment

  CloudTrailBucket:
    Type: AWS::S3::Bucket
    Properties:
      # Remove explicit bucket name to avoid conflicts - AWS will generate unique name
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: aws:kms
              KMSMasterKeyID: !GetAtt S3KMSKey.Arn
            BucketKeyEnabled: true
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 30
          - Id: ArchiveOldLogs
            Status: Enabled
            ExpirationInDays: 2555  # 7 years
            Transitions:
              - TransitionInDays: 30
                StorageClass: STANDARD_IA
              - TransitionInDays: 90
                StorageClass: GLACIER
              - TransitionInDays: 365
                StorageClass: DEEP_ARCHIVE
      LoggingConfiguration:
        DestinationBucketName: !Ref CloudTrailAccessLogsBucket
        LogFilePrefix: cloudtrail-access-logs/
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-cloudtrail-bucket'
        - Key: Environment
          Value: !Ref Environment

  # S3 bucket for CloudTrail access logs
  CloudTrailAccessLogsBucket:
    Type: AWS::S3::Bucket
    Properties:
      # Remove explicit bucket name to avoid conflicts - AWS will generate unique name
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: aws:kms
              KMSMasterKeyID: !GetAtt S3KMSKey.Arn
            BucketKeyEnabled: true
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldAccessLogs
            Status: Enabled
            ExpirationInDays: 90
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-cloudtrail-access-logs-bucket'
        - Key: Environment
          Value: !Ref Environment

  CloudTrailBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref CloudTrailBucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: AWSCloudTrailAclCheck
            Effect: Allow
            Principal:
              Service: cloudtrail.amazonaws.com
            Action: s3:GetBucketAcl
            Resource: !GetAtt CloudTrailBucket.Arn
          - Sid: AWSCloudTrailWrite
            Effect: Allow
            Principal:
              Service: cloudtrail.amazonaws.com
            Action: s3:PutObject
            Resource: !Sub '${CloudTrailBucket.Arn}/*'
            Condition:
              StringEquals:
                's3:x-amz-acl': bucket-owner-full-control
          - Sid: AWSCloudTrailGetBucketLocation
            Effect: Allow
            Principal:
              Service: cloudtrail.amazonaws.com
            Action: s3:GetBucketLocation
            Resource: !GetAtt CloudTrailBucket.Arn
          - Sid: DenyInsecureConnections
            Effect: Deny
            Principal: '*'
            Action: 's3:*'
            Resource:
              - !GetAtt CloudTrailBucket.Arn
              - !Sub '${CloudTrailBucket.Arn}/*'
            Condition:
              Bool:
                'aws:SecureTransport': 'false'

Outputs:
  DatabaseKMSKeyId:
    Description: Database KMS Key ID
    Value: !Ref DatabaseKMSKey
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseKMSKeyId'

  SecretsManagerKMSKeyId:
    Description: Secrets Manager KMS Key ID
    Value: !Ref SecretsManagerKMSKey
    Export:
      Name: !Sub '${AWS::StackName}-SecretsManagerKMSKeyId'

  CloudWatchLogsKMSKeyId:
    Description: CloudWatch Logs KMS Key ID
    Value: !Ref CloudWatchLogsKMSKey
    Export:
      Name: !Sub '${AWS::StackName}-CloudWatchLogsKMSKeyId'

  S3KMSKeyId:
    Description: S3 KMS Key ID
    Value: !Ref S3KMSKey
    Export:
      Name: !Sub '${AWS::StackName}-S3KMSKeyId'

  ALBSecurityGroupId:
    Description: Application Load Balancer Security Group ID
    Value: !Ref ALBSecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-ALBSecurityGroupId'

  ECSSecurityGroupId:
    Description: ECS Security Group ID
    Value: !Ref ECSSecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-ECSSecurityGroupId'

  DatabaseSecurityGroupId:
    Description: Database Security Group ID
    Value: !Ref DatabaseSecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseSecurityGroupId'

  RDSProxySecurityGroupId:
    Description: RDS Proxy Security Group ID
    Value: !Ref RDSProxySecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-RDSProxySecurityGroupId'

  CacheSecurityGroupId:
    Description: Cache Security Group ID
    Value: !Ref CacheSecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-CacheSecurityGroupId'

  ECSTaskExecutionRoleArn:
    Description: ECS Task Execution Role ARN
    Value: !GetAtt ECSTaskExecutionRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-ECSTaskExecutionRoleArn'

  ECSTaskRoleArn:
    Description: ECS Task Role ARN
    Value: !GetAtt ECSTaskRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-ECSTaskRoleArn'

  RDSProxyRoleArn:
    Description: RDS Proxy Role ARN
    Value: !GetAtt RDSProxyRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-RDSProxyRoleArn'

  AutoScalingRoleArn:
    Description: Auto Scaling Role ARN
    Value: !GetAtt AutoScalingRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-AutoScalingRoleArn'

  AlertsTopicArn:
    Description: SNS Topic ARN for alerts
    Value: !Ref AlertsTopic
    Export:
      Name: !Sub '${AWS::StackName}-AlertsTopicArn'

  WebACLArn:
    Description: WAF Web ACL ARN
    Value: !GetAtt WebACL.Arn
    Export:
      Name: !Sub '${AWS::StackName}-WebACLArn'

  CloudTrailBucketName:
    Description: CloudTrail S3 Bucket Name
    Value: !Ref CloudTrailBucket
    Export:
      Name: !Sub '${AWS::StackName}-CloudTrailBucketName'