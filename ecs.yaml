AWSTemplateFormatVersion: '2010-09-09'
Description: 'ECS Fargate cluster with microservices for Trading Platform'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ProjectName:
    Type: String
    Description: Project name for resource naming

  VpcId:
    Type: String
    Description: VPC ID where ECS cluster will be created

  PrivateSubnet1Id:
    Type: String
    Description: Private Subnet 1 ID

  PrivateSubnet2Id:
    Type: String
    Description: Private Subnet 2 ID

  PrivateSubnet3Id:
    Type: String
    Description: Private Subnet 3 ID

  ECSSecurityGroupId:
    Type: String
    Description: Security Group ID for ECS tasks

  ECSClusterName:
    Type: String
    Description: Name for the ECS cluster

  ContainerImageRepository:
    Type: String
    Description: ECR repository base URL

  ContainerImageTag:
    Type: String
    Description: Container image tag

  TradingApiTaskCpu:
    Type: Number
    Description: Task CPU units for Trading API

  TradingApiTaskMemory:
    Type: Number
    Description: Task memory in MB for Trading API

  TradingApiMinCapacity:
    Type: Number
    Description: Minimum number of Trading API tasks

  TradingApiMaxCapacity:
    Type: Number
    Description: Maximum number of Trading API tasks

  DatabaseProxyEndpoint:
    Type: String
    Description: RDS Proxy endpoint

  SecretsManagerArn:
    Type: String
    Description: Secrets Manager ARN for database credentials

Resources:
  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Ref ECSClusterName
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1
          Base: 1
        - CapacityProvider: FARGATE_SPOT
          Weight: 4
      ClusterSettings:
        - Name: containerInsights
          Value: enabled
      Tags:
        - Key: Name
          Value: !Ref ECSClusterName
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Log Groups
  TradingAPILogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/trading-api'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-logs'
        - Key: Environment
          Value: !Ref Environment

  RiskEngineLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/risk-engine'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-logs'
        - Key: Environment
          Value: !Ref Environment

  FraudDetectionLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/fraud-detection'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-logs'
        - Key: Environment
          Value: !Ref Environment

  MarketDataLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/market-data'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-logs'
        - Key: Environment
          Value: !Ref Environment

  # Task Definitions
  TradingAPITaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-trading-api'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: !Ref TradingApiTaskCpu
      Memory: !Ref TradingApiTaskMemory
      ContainerDefinitions:
        - Name: trading-api
          Image: !Sub '${ContainerImageRepository}/trading-api:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: DB_PROXY_ENDPOINT
              Value: !Ref DatabaseProxyEndpoint
            - Name: DB_NAME
              Value: trading_db
            - Name: DB_PORT
              Value: '5432'
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
            - Name: APP_MESH_VIRTUAL_NODE_NAME
              Value: !Sub 'mesh/${ProjectName}-${Environment}/virtualNode/trading-api'
          Secrets:
            - Name: DB_USERNAME
              ValueFrom: !Sub '${SecretsManagerArn}:username::'
            - Name: DB_PASSWORD
              ValueFrom: !Sub '${SecretsManagerArn}:password::'
            - Name: JWT_SECRET
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ProjectName}-${Environment}-jwt-secret'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TradingAPILogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          WorkingDirectory: /app
          User: '1001:1001'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-task'
        - Key: Environment
          Value: !Ref Environment

  RiskEngineTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-risk-engine'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 4096
      Memory: 8192
      ContainerDefinitions:
        - Name: risk-engine
          Image: !Sub '${ContainerImageRepository}/risk-engine:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: DB_PROXY_ENDPOINT
              Value: !Ref DatabaseProxyEndpoint
            - Name: DB_NAME
              Value: trading_db
            - Name: DB_PORT
              Value: '5432'
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
            - Name: APP_MESH_VIRTUAL_NODE_NAME
              Value: !Sub 'mesh/${ProjectName}-${Environment}/virtualNode/risk-engine'
          Secrets:
            - Name: DB_USERNAME
              ValueFrom: !Sub '${SecretsManagerArn}:username::'
            - Name: DB_PASSWORD
              ValueFrom: !Sub '${SecretsManagerArn}:password::'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref RiskEngineLogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          WorkingDirectory: /app
          User: '1001:1001'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-task'
        - Key: Environment
          Value: !Ref Environment

  FraudDetectionTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-fraud-detection'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 4096
      Memory: 8192
      ContainerDefinitions:
        - Name: fraud-detection
          Image: !Sub '${ContainerImageRepository}/fraud-detection:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: DB_PROXY_ENDPOINT
              Value: !Ref DatabaseProxyEndpoint
            - Name: DB_NAME
              Value: trading_db
            - Name: DB_PORT
              Value: '5432'
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
            - Name: APP_MESH_VIRTUAL_NODE_NAME
              Value: !Sub 'mesh/${ProjectName}-${Environment}/virtualNode/fraud-detection'
          Secrets:
            - Name: DB_USERNAME
              ValueFrom: !Sub '${SecretsManagerArn}:username::'
            - Name: DB_PASSWORD
              ValueFrom: !Sub '${SecretsManagerArn}:password::'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref FraudDetectionLogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          WorkingDirectory: /app
          User: '1001:1001'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-task'
        - Key: Environment
          Value: !Ref Environment

  MarketDataTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-market-data'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 2048
      Memory: 4096
      ContainerDefinitions:
        - Name: market-data
          Image: !Sub '${ContainerImageRepository}/market-data:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
            - Name: APP_MESH_VIRTUAL_NODE_NAME
              Value: !Sub 'mesh/${ProjectName}-${Environment}/virtualNode/market-data'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref MarketDataLogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          WorkingDirectory: /app
          User: '1001:1001'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-task'
        - Key: Environment
          Value: !Ref Environment

  # ECS Services
  TradingAPIService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-trading-api'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref TradingAPITaskDefinition
      LaunchType: FARGATE
      DesiredCount: !Ref TradingApiMinCapacity
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Sub 'arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:targetgroup/${ProjectName}-${Environment}-trading-api-tg/*'
          ContainerName: trading-api
          ContainerPort: 8080
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  RiskEngineService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-risk-engine'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref RiskEngineTaskDefinition
      LaunchType: FARGATE
      DesiredCount: 3
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Sub 'arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:targetgroup/${ProjectName}-${Environment}-risk-engine-tg/*'
          ContainerName: risk-engine
          ContainerPort: 8080
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  FraudDetectionService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-fraud-detection'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref FraudDetectionTaskDefinition
      LaunchType: FARGATE
      DesiredCount: 2
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Sub 'arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:targetgroup/${ProjectName}-${Environment}-fraud-detection-tg/*'
          ContainerName: fraud-detection
          ContainerPort: 8080
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  MarketDataService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-market-data'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref MarketDataTaskDefinition
      LaunchType: FARGATE
      DesiredCount: 2
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Sub 'arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:targetgroup/${ProjectName}-${Environment}-market-data-tg/*'
          ContainerName: market-data
          ContainerPort: 8080
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  # Auto Scaling
  TradingAPIAutoScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref TradingApiMaxCapacity
      MinCapacity: !Ref TradingApiMinCapacity
      ResourceId: !Sub 'service/${ECSCluster}/${TradingAPIService.Name}'
      # Remove RoleARN - AWS will use service-linked role automatically for ECS
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs

  TradingAPIAutoScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub '${ProjectName}-${Environment}-trading-api-scaling'
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref TradingAPIAutoScalingTarget
      TargetTrackingScalingPolicyConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        TargetValue: 70.0
        ScaleOutCooldown: 300
        ScaleInCooldown: 900

  # Service Discovery
  ServiceDiscoveryNamespace:
    Type: AWS::ServiceDiscovery::PrivateDnsNamespace
    Properties:
      Name: !Sub '${ProjectName}-${Environment}.local'
      Vpc: !Ref VpcId
      Description: !Sub 'Service discovery namespace for ${ProjectName} ${Environment}'

  TradingAPIServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: trading-api
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

  RiskEngineServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: risk-engine
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

  FraudDetectionServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: fraud-detection
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

  MarketDataServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: market-data
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

Outputs:
  ECSClusterName:
    Description: ECS cluster name
    Value: !Ref ECSCluster
    Export:
      Name: !Sub '${AWS::StackName}-ECSClusterName'

  ECSClusterArn:
    Description: ECS cluster ARN
    Value: !GetAtt ECSCluster.Arn
    Export:
      Name: !Sub '${AWS::StackName}-ECSClusterArn'

  TradingAPIServiceName:
    Description: Trading API service name
    Value: !GetAtt TradingAPIService.Name
    Export:
      Name: !Sub '${AWS::StackName}-TradingAPIServiceName'

  RiskEngineServiceName:
    Description: Risk Engine service name
    Value: !GetAtt RiskEngineService.Name
    Export:
      Name: !Sub '${AWS::StackName}-RiskEngineServiceName'

  FraudDetectionServiceName:
    Description: Fraud Detection service name
    Value: !GetAtt FraudDetectionService.Name
    Export:
      Name: !Sub '${AWS::StackName}-FraudDetectionServiceName'

  MarketDataServiceName:
    Description: Market Data service name
    Value: !GetAtt MarketDataService.Name
    Export:
      Name: !Sub '${AWS::StackName}-MarketDataServiceName'

  ServiceDiscoveryNamespaceId:
    Description: Service Discovery namespace ID
    Value: !Ref ServiceDiscoveryNamespace
    Export:
      Name: !Sub '${AWS::StackName}-ServiceDiscoveryNamespaceId'