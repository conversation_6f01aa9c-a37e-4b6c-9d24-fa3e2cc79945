AWSTemplateFormatVersion: '2010-09-09'
Description: 'ECS Fargate cluster with microservices for Trading Platform'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ProjectName:
    Type: String
    Description: Project name for resource naming

  VpcId:
    Type: String
    Description: VPC ID where ECS cluster will be created

  PrivateSubnet1Id:
    Type: String
    Description: Private Subnet 1 ID

  PrivateSubnet2Id:
    Type: String
    Description: Private Subnet 2 ID

  PrivateSubnet3Id:
    Type: String
    Description: Private Subnet 3 ID

  ECSSecurityGroupId:
    Type: String
    Description: Security Group ID for ECS tasks

  ECSClusterName:
    Type: String
    Description: Name for the ECS cluster

  ContainerImageRepository:
    Type: String
    Description: ECR repository base URL

  ContainerImageTag:
    Type: String
    Description: Container image tag

  TradingApiTaskCpu:
    Type: Number
    Description: Task CPU units for Trading API

  TradingApiTaskMemory:
    Type: Number
    Description: Task memory in MB for Trading API

  TradingApiMinCapacity:
    Type: Number
    Description: Minimum number of Trading API tasks

  TradingApiMaxCapacity:
    Type: Number
    Description: Maximum number of Trading API tasks

  DatabaseProxyEndpoint:
    Type: String
    Description: RDS Proxy endpoint

  SecretsManagerArn:
    Type: String
    Description: Secrets Manager ARN for database credentials

  # FIXED: Added missing parameters
  TradingAPITargetGroupArn:
    Type: String
    Description: Trading API Target Group ARN

  RiskEngineTargetGroupArn:
    Type: String
    Description: Risk Engine Target Group ARN

  FraudDetectionTargetGroupArn:
    Type: String
    Description: Fraud Detection Target Group ARN

  MarketDataTargetGroupArn:
    Type: String
    Description: Market Data Target Group ARN

Resources:
  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Ref ECSClusterName
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1
          Base: 1
        - CapacityProvider: FARGATE_SPOT
          Weight: 4
      ClusterSettings:
        - Name: containerInsights
          Value: enabled
      Tags:
        - Key: Name
          Value: !Ref ECSClusterName
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Log Groups
  TradingAPILogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/trading-api'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-logs'
        - Key: Environment
          Value: !Ref Environment

  RiskEngineLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/risk-engine'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-logs'
        - Key: Environment
          Value: !Ref Environment

  FraudDetectionLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/fraud-detection'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-logs'
        - Key: Environment
          Value: !Ref Environment

  MarketDataLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ecs/${ProjectName}-${Environment}/market-data'
      RetentionInDays: 30
      KmsKeyId: !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-logs'
        - Key: Environment
          Value: !Ref Environment

  # Task Definitions with FIXED security configurations
  TradingAPITaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-trading-api'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: !Ref TradingApiTaskCpu
      Memory: !Ref TradingApiTaskMemory
      # FIXED: Added platform version and runtime platform
      RuntimePlatform:
        CpuArchitecture: X86_64
        OperatingSystemFamily: LINUX
      ContainerDefinitions:
        - Name: trading-api
          Image: !Sub '${ContainerImageRepository}/trading-api:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
              Name: http
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: DB_PROXY_ENDPOINT
              Value: !Ref DatabaseProxyEndpoint
            - Name: DB_NAME
              Value: trading_db
            - Name: DB_PORT
              Value: '5432'
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
            # FIXED: Removed App Mesh reference for now
          Secrets:
            - Name: DB_USERNAME
              ValueFrom: !Sub '${SecretsManagerArn}:username::'
            - Name: DB_PASSWORD
              ValueFrom: !Sub '${SecretsManagerArn}:password::'
            - Name: JWT_SECRET
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ProjectName}-${Environment}-jwt-secret'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TradingAPILogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
              # FIXED: Added non-blocking mode for better performance
              mode: non-blocking
              max-buffer-size: 25m
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          # FIXED: Security improvements - read-only root filesystem and non-root user
          ReadonlyRootFilesystem: true
          User: '1001:1001'
          # FIXED: Add temporary file system for writable directories
          MountPoints:
            - SourceVolume: tmp-volume
              ContainerPath: /tmp
              ReadOnly: false
          WorkingDirectory: /app
      # FIXED: Add volumes for temporary files
      Volumes:
        - Name: tmp-volume
          Host: {}
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-task'
        - Key: Environment
          Value: !Ref Environment

  RiskEngineTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-risk-engine'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 4096
      Memory: 8192
      RuntimePlatform:
        CpuArchitecture: X86_64
        OperatingSystemFamily: LINUX
      ContainerDefinitions:
        - Name: risk-engine
          Image: !Sub '${ContainerImageRepository}/risk-engine:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
              Name: http
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: DB_PROXY_ENDPOINT
              Value: !Ref DatabaseProxyEndpoint
            - Name: DB_NAME
              Value: trading_db
            - Name: DB_PORT
              Value: '5432'
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
          Secrets:
            - Name: DB_USERNAME
              ValueFrom: !Sub '${SecretsManagerArn}:username::'
            - Name: DB_PASSWORD
              ValueFrom: !Sub '${SecretsManagerArn}:password::'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref RiskEngineLogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
              mode: non-blocking
              max-buffer-size: 25m
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          User: '1001:1001'
          MountPoints:
            - SourceVolume: tmp-volume
              ContainerPath: /tmp
              ReadOnly: false
          WorkingDirectory: /app
      Volumes:
        - Name: tmp-volume
          Host: {}
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-task'
        - Key: Environment
          Value: !Ref Environment

  FraudDetectionTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-fraud-detection'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 4096
      Memory: 8192
      RuntimePlatform:
        CpuArchitecture: X86_64
        OperatingSystemFamily: LINUX
      ContainerDefinitions:
        - Name: fraud-detection
          Image: !Sub '${ContainerImageRepository}/fraud-detection:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
              Name: http
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: DB_PROXY_ENDPOINT
              Value: !Ref DatabaseProxyEndpoint
            - Name: DB_NAME
              Value: trading_db
            - Name: DB_PORT
              Value: '5432'
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
          Secrets:
            - Name: DB_USERNAME
              ValueFrom: !Sub '${SecretsManagerArn}:username::'
            - Name: DB_PASSWORD
              ValueFrom: !Sub '${SecretsManagerArn}:password::'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref FraudDetectionLogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
              mode: non-blocking
              max-buffer-size: 25m
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          User: '1001:1001'
          MountPoints:
            - SourceVolume: tmp-volume
              ContainerPath: /tmp
              ReadOnly: false
          WorkingDirectory: /app
      Volumes:
        - Name: tmp-volume
          Host: {}
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-task'
        - Key: Environment
          Value: !Ref Environment

  MarketDataTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${ProjectName}-${Environment}-market-data'
      TaskRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-role'
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/${ProjectName}-${Environment}-ecs-task-execution-role'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 2048
      Memory: 4096
      RuntimePlatform:
        CpuArchitecture: X86_64
        OperatingSystemFamily: LINUX
      ContainerDefinitions:
        - Name: market-data
          Image: !Sub '${ContainerImageRepository}/market-data:${ContainerImageTag}'
          Essential: true
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
              Name: http
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: REDIS_ENDPOINT
              Value: !Sub '${ProjectName}-${Environment}-cache.abc123.cache.amazonaws.com'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref MarketDataLogGroup
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
              mode: non-blocking
              max-buffer-size: 25m
          HealthCheck:
            Command:
              - CMD-SHELL
              - 'curl -f http://localhost:8080/health || exit 1'
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          LinuxParameters:
            InitProcessEnabled: true
          ReadonlyRootFilesystem: true
          User: '1001:1001'
          MountPoints:
            - SourceVolume: tmp-volume
              ContainerPath: /tmp
              ReadOnly: false
          WorkingDirectory: /app
      Volumes:
        - Name: tmp-volume
          Host: {}
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-task'
        - Key: Environment
          Value: !Ref Environment

  # FIXED: ECS Services with proper target group references
  TradingAPIService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-trading-api'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref TradingAPITaskDefinition
      LaunchType: FARGATE
      DesiredCount: !Ref TradingApiMinCapacity
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      # FIXED: Use parameter reference instead of hardcoded ARN
      LoadBalancers:
        - TargetGroupArn: !Ref TradingAPITargetGroupArn
          ContainerName: trading-api
          ContainerPort: 8080
      # FIXED: Add health check grace period
      HealthCheckGracePeriodSeconds: 60
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-trading-api-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  RiskEngineService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-risk-engine'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref RiskEngineTaskDefinition
      LaunchType: FARGATE
      DesiredCount: 3
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Ref RiskEngineTargetGroupArn
          ContainerName: risk-engine
          ContainerPort: 8080
      HealthCheckGracePeriodSeconds: 60
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-risk-engine-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  FraudDetectionService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-fraud-detection'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref FraudDetectionTaskDefinition
      LaunchType: FARGATE
      DesiredCount: 2
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Ref FraudDetectionTargetGroupArn
          ContainerName: fraud-detection
          ContainerPort: 8080
      HealthCheckGracePeriodSeconds: 60
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-fraud-detection-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  MarketDataService:
    Type: AWS::ECS::Service
    Properties:
      ServiceName: !Sub '${ProjectName}-${Environment}-market-data'
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref MarketDataTaskDefinition
      LaunchType: FARGATE
      DesiredCount: 2
      PlatformVersion: LATEST
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups:
            - !Ref ECSSecurityGroupId
          Subnets:
            - !Ref PrivateSubnet1Id
            - !Ref PrivateSubnet2Id
            - !Ref PrivateSubnet3Id
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Ref MarketDataTargetGroupArn
          ContainerName: market-data
          ContainerPort: 8080
      HealthCheckGracePeriodSeconds: 60
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-market-data-service'
        - Key: Environment
          Value: !Ref Environment
      EnableExecuteCommand: true
      PropagateTags: SERVICE

  # Auto Scaling
  TradingAPIAutoScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: !Ref TradingApiMaxCapacity
      MinCapacity: !Ref TradingApiMinCapacity
      ResourceId: !Sub 'service/${ECSCluster}/${TradingAPIService.Name}'
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs

  TradingAPIAutoScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub '${ProjectName}-${Environment}-trading-api-scaling'
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref TradingAPIAutoScalingTarget
      TargetTrackingScalingPolicyConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        TargetValue: 70.0
        ScaleOutCooldown: 300
        ScaleInCooldown: 900

  # FIXED: Add auto scaling for other critical services
  RiskEngineAutoScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MaxCapacity: 10
      MinCapacity: 3
      ResourceId: !Sub 'service/${ECSCluster}/${RiskEngineService.Name}'
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs

  RiskEngineAutoScalingPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub '${ProjectName}-${Environment}-risk-engine-scaling'
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref RiskEngineAutoScalingTarget
      TargetTrackingScalingPolicyConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ECSServiceAverageCPUUtilization
        TargetValue: 60.0  # Lower threshold for risk engine
        ScaleOutCooldown: 180  # Faster scaling for risk calculations
        ScaleInCooldown: 600

  # Service Discovery
  ServiceDiscoveryNamespace:
    Type: AWS::ServiceDiscovery::PrivateDnsNamespace
    Properties:
      Name: !Sub '${ProjectName}-${Environment}.local'
      Vpc: !Ref VpcId
      Description: !Sub 'Service discovery namespace for ${ProjectName} ${Environment}'

  TradingAPIServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: trading-api
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

  RiskEngineServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: risk-engine
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

  FraudDetectionServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: fraud-detection
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

  MarketDataServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: market-data
      NamespaceId: !Ref ServiceDiscoveryNamespace
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 3

Outputs:
  ECSClusterName:
    Description: ECS cluster name
    Value: !Ref ECSCluster
    Export:
      Name: !Sub '${AWS::StackName}-ECSClusterName'

  ECSClusterArn:
    Description: ECS cluster ARN
    Value: !GetAtt ECSCluster.Arn
    Export:
      Name: !Sub '${AWS::StackName}-ECSClusterArn'

  TradingAPIServiceName:
    Description: Trading API service name
    Value: !GetAtt TradingAPIService.Name
    Export:
      Name: !Sub '${AWS::StackName}-TradingAPIServiceName'

  TradingAPIServiceArn:
    Description: Trading API service ARN
    Value: !Ref TradingAPIService
    Export:
      Name: !Sub '${AWS::StackName}-TradingAPIServiceArn'

  RiskEngineServiceName:
    Description: Risk Engine service name
    Value: !GetAtt RiskEngineService.Name
    Export:
      Name: !Sub '${AWS::StackName}-RiskEngineServiceName'

  RiskEngineServiceArn:
    Description: Risk Engine service ARN
    Value: !Ref RiskEngineService
    Export:
      Name: !Sub '${AWS::StackName}-RiskEngineServiceArn'

  FraudDetectionServiceName:
    Description: Fraud Detection service name
    Value: !GetAtt FraudDetectionService.Name
    Export:
      Name: !Sub '${AWS::StackName}-FraudDetectionServiceName'

  FraudDetectionServiceArn:
    Description: Fraud Detection service ARN
    Value: !Ref FraudDetectionService
    Export:
      Name: !Sub '${AWS::StackName}-FraudDetectionServiceArn'

  MarketDataServiceName:
    Description: Market Data service name
    Value: !GetAtt MarketDataService.Name
    Export:
      Name: !Sub '${AWS::StackName}-MarketDataServiceName'

  MarketDataServiceArn:
    Description: Market Data service ARN
    Value: !Ref MarketDataService
    Export:
      Name: !Sub '${AWS::StackName}-MarketDataServiceArn'

  ServiceDiscoveryNamespaceId:
    Description: Service Discovery namespace ID
    Value: !Ref ServiceDiscoveryNamespace
    Export:
      Name: !Sub '${AWS::StackName}-ServiceDiscoveryNamespaceId'