AWSTemplateFormatVersion: '2010-09-09'
Description: 'Aurora PostgreSQL database cluster with RDS Proxy for Trading Platform'

Parameters:
  Environment:
    Type: String
    Description: Environment name

  ProjectName:
    Type: String
    Description: Project name for resource naming

  DatabaseSubnet1Id:
    Type: String
    Description: Database Subnet 1 ID

  DatabaseSubnet2Id:
    Type: String
    Description: Database Subnet 2 ID

  DatabaseSubnet3Id:
    Type: String
    Description: Database Subnet 3 ID

  DatabaseSecurityGroupId:
    Type: String
    Description: Security Group ID for database

  DatabaseInstanceClass:
    Type: String
    Description: Database instance class for primary instance

  DatabaseReaderInstanceClass:
    Type: String
    Description: Database instance class for reader instances

  DatabaseEngine:
    Type: String
    Description: Database engine

  DatabaseUsername:
    Type: String
    Description: Database master username
    NoEcho: true

  DatabaseName:
    Type: String
    Description: Database name

  DeletionProtection:
    Type: String
    Description: Enable deletion protection for critical resources

  BackupRetentionPeriod:
    Type: Number
    Description: Number of days to retain database backups

  EnablePerformanceInsights:
    Type: String
    Description: Enable Performance Insights

  KMSKeyId:
    Type: String
    Description: KMS Key ID for encryption

  RDSProxyRoleArn:
    Type: String
    Description: RDS Proxy Role ARN

Mappings:
  EngineVersionMap:
    aurora-postgresql:
      EngineVersion: '15.4'
      ParameterGroupFamily: 'aurora-postgresql15'
      Port: 5432

Resources:
  # RDS Service-Linked Role removed - already exists in account
  # AWS will use the existing service-linked role automatically

  # Secrets Manager for Database Password
  DatabaseSecret:
    Type: AWS::SecretsManager::Secret
    # DeletionPolicy: Retain
    # UpdateReplacePolicy: Retain
    Properties:
      Name: !Sub '${ProjectName}-${Environment}-db-credential'
      Description: !Sub 'Aurora PostgreSQL credentials for ${ProjectName} ${Environment}'
      KmsKeyId: !Ref KMSKeyId
      GenerateSecretString:
        SecretStringTemplate: !Sub '{"username": "${DatabaseUsername}"}'
        GenerateStringKey: 'password'
        PasswordLength: 32
        ExcludeCharacters: '"@/\'
      # Remove ReplicaRegions - cannot replicate to same region
      # Cross-region replication can be added later if needed
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-aurora-secret'
        - Key: Environment
          Value: !Ref Environment

  # Database Subnet Group
  DatabaseSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupName: !Sub '${ProjectName}-${Environment}-database-subnet-group'
      DBSubnetGroupDescription: Subnet group for Aurora PostgreSQL cluster
      SubnetIds:
        - !Ref DatabaseSubnet1Id
        - !Ref DatabaseSubnet2Id
        - !Ref DatabaseSubnet3Id
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-database-subnet-group'
        - Key: Environment
          Value: !Ref Environment

  # Custom DB Cluster Parameter Group
  DatabaseClusterParameterGroup:
    Type: AWS::RDS::DBClusterParameterGroup
    Properties:
      Family: !FindInMap [EngineVersionMap, !Ref DatabaseEngine, ParameterGroupFamily]
      Description: !Sub 'Custom cluster parameter group for ${ProjectName} ${Environment}'
      Parameters:
        # Use only Aurora PostgreSQL compatible cluster parameters
        shared_preload_libraries: 'pg_stat_statements'
        log_statement: 'all'
        log_min_duration_statement: '100'
        log_connections: '1'
        log_disconnections: '1'
        log_lock_waits: '1'
        track_activity_query_size: '2048'
        track_io_timing: '1'
        # Remove Aurora-managed parameters
        # max_wal_senders and max_replication_slots are managed by Aurora
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-cluster-parameter-group'
        - Key: Environment
          Value: !Ref Environment

  # Custom DB Parameter Group for instances
  DatabaseParameterGroup:
    Type: AWS::RDS::DBParameterGroup
    Properties:
      Family: !FindInMap [EngineVersionMap, !Ref DatabaseEngine, ParameterGroupFamily]
      Description: !Sub 'Custom parameter group for ${ProjectName} ${Environment}'
      Parameters:
        # Use only essential Aurora PostgreSQL compatible parameters
        shared_buffers: '{DBInstanceClassMemory/4}'
        effective_cache_size: '{DBInstanceClassMemory*3/4}'
        work_mem: '8192'
        maintenance_work_mem: '262144'
        random_page_cost: '1.1'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-parameter-group'
        - Key: Environment
          Value: !Ref Environment

  # Aurora PostgreSQL Cluster
  DatabaseCluster:
    Type: AWS::RDS::DBCluster
    DependsOn:
      - DatabaseClusterParameterGroup
      - DatabaseParameterGroup
      - DatabaseSubnetGroup
      - DatabaseSecret
    DeletionPolicy: Snapshot
    UpdateReplacePolicy: Snapshot
    Properties:
      DBClusterIdentifier: !Sub '${ProjectName}-${Environment}-aurora-cluster'
      Engine: !Ref DatabaseEngine
      EngineVersion: !FindInMap [EngineVersionMap, !Ref DatabaseEngine, EngineVersion]
      DatabaseName: !Ref DatabaseName
      MasterUsername: !Sub '{{resolve:secretsmanager:${DatabaseSecret}:SecretString:username}}'
      MasterUserPassword: !Sub '{{resolve:secretsmanager:${DatabaseSecret}:SecretString:password}}'
      BackupRetentionPeriod: !Ref BackupRetentionPeriod
      PreferredBackupWindow: '03:00-04:00'
      PreferredMaintenanceWindow: 'Sun:04:00-Sun:05:00'
      Port: !FindInMap [EngineVersionMap, !Ref DatabaseEngine, Port]
      VpcSecurityGroupIds:
        - !Ref DatabaseSecurityGroupId
      DBSubnetGroupName: !Ref DatabaseSubnetGroup
      DBClusterParameterGroupName: !Ref DatabaseClusterParameterGroup
      StorageEncrypted: true
      KmsKeyId: !Ref KMSKeyId
      DeletionProtection: !Ref DeletionProtection
      EnableCloudwatchLogsExports:
        - postgresql
      # Remove AssociatedRoles - can be added later if needed
      CopyTagsToSnapshot: true
      # Remove EnableHttpEndpoint - not needed for basic setup
      # Remove EnableIAMDatabaseAuthentication - can be added later
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-aurora-cluster'
        - Key: Environment
          Value: !Ref Environment

  # Primary Database Instance
  DatabasePrimaryInstance:
    Type: AWS::RDS::DBInstance
    DependsOn: DatabaseCluster
    # DeletionPolicy: Snapshot
    # UpdateReplacePolicy: Snapshot
    Properties:
      DBInstanceIdentifier: !Sub '${ProjectName}-${Environment}-aurora-primary'
      DBClusterIdentifier: !Ref DatabaseCluster
      Engine: !Ref DatabaseEngine
      DBInstanceClass: !Ref DatabaseInstanceClass
      DBParameterGroupName: !Ref DatabaseParameterGroup
      PubliclyAccessible: false
      StorageEncrypted: true
      EnablePerformanceInsights: !Ref EnablePerformanceInsights
      PerformanceInsightsKMSKeyId: !Ref KMSKeyId
      PerformanceInsightsRetentionPeriod: 7
      # Remove MonitoringInterval and MonitoringRoleArn - can be added later if needed
      # DeletionProtection removed - only allowed at cluster level for Aurora
      # MultiAZ removed - not supported for Aurora instances (Aurora has built-in HA)
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-aurora-primary'
        - Key: Environment
          Value: !Ref Environment

  # Reader Database Instance 1
  DatabaseReaderInstance1:
    Type: AWS::RDS::DBInstance
    DependsOn: DatabasePrimaryInstance
    # DeletionPolicy: Delete
    # UpdateReplacePolicy: Delete
    Properties:
      DBInstanceIdentifier: !Sub '${ProjectName}-${Environment}-aurora-reader-1'
      DBClusterIdentifier: !Ref DatabaseCluster
      Engine: !Ref DatabaseEngine
      DBInstanceClass: !Ref DatabaseReaderInstanceClass
      DBParameterGroupName: !Ref DatabaseParameterGroup
      PubliclyAccessible: false
      StorageEncrypted: true
      EnablePerformanceInsights: !Ref EnablePerformanceInsights
      PerformanceInsightsKMSKeyId: !Ref KMSKeyId
      PerformanceInsightsRetentionPeriod: 7
      # Remove MonitoringInterval and MonitoringRoleArn - can be added later if needed
      # DeletionProtection removed - only allowed at cluster level for Aurora
      # MultiAZ removed - not supported for Aurora instances (Aurora has built-in HA)
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-aurora-reader-1'
        - Key: Environment
          Value: !Ref Environment

  # Reader Database Instance 2
  DatabaseReaderInstance2:
    Type: AWS::RDS::DBInstance
    DependsOn: DatabaseReaderInstance1
    # DeletionPolicy: Delete
    # UpdateReplacePolicy: Delete
    Properties:
      DBInstanceIdentifier: !Sub '${ProjectName}-${Environment}-aurora-reader-2'
      DBClusterIdentifier: !Ref DatabaseCluster
      Engine: !Ref DatabaseEngine
      DBInstanceClass: !Ref DatabaseReaderInstanceClass
      DBParameterGroupName: !Ref DatabaseParameterGroup
      PubliclyAccessible: false
      StorageEncrypted: true
      EnablePerformanceInsights: !Ref EnablePerformanceInsights
      PerformanceInsightsKMSKeyId: !Ref KMSKeyId
      PerformanceInsightsRetentionPeriod: 7
      # Remove MonitoringInterval and MonitoringRoleArn - can be added later if needed
      # DeletionProtection removed - only allowed at cluster level for Aurora
      # MultiAZ removed - not supported for Aurora instances (Aurora has built-in HA)
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-aurora-reader-2'
        - Key: Environment
          Value: !Ref Environment

  # RDS Proxy
  DatabaseProxy:
    Type: AWS::RDS::DBProxy
    DependsOn:
      - DatabasePrimaryInstance
      - DatabaseReaderInstance1
    Properties:
      DBProxyName: !Sub '${ProjectName}-${Environment}-aurora-proxy'
      EngineFamily: POSTGRESQL
      Auth:
        - AuthScheme: SECRETS
          SecretArn: !Ref DatabaseSecret
          IAMAuth: DISABLED
      RoleArn: !Ref RDSProxyRoleArn
      VpcSubnetIds:
        - !Ref DatabaseSubnet1Id
        - !Ref DatabaseSubnet2Id
        - !Ref DatabaseSubnet3Id
      VpcSecurityGroupIds:
        - !Ref DatabaseSecurityGroupId
      RequireTLS: true
      IdleClientTimeout: 1800
      DebugLogging: false
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-aurora-proxy'
        - Key: Environment
          Value: !Ref Environment

  DatabaseProxyTargetGroup:
    Type: AWS::RDS::DBProxyTargetGroup
    Properties:
      DBProxyName: !Ref DatabaseProxy
      TargetGroupName: default
      DBClusterIdentifiers:
        - !Ref DatabaseCluster
      ConnectionPoolConfigurationInfo:
        MaxConnectionsPercent: 100
        MaxIdleConnectionsPercent: 50
        SessionPinningFilters:
          - EXCLUDE_VARIABLE_SETS

  # CloudWatch Log Groups
  DatabaseLogGroup:
    Type: AWS::Logs::LogGroup
    # DeletionPolicy: Retain
    # UpdateReplacePolicy: Retain
    Properties:
      LogGroupName: !Sub '/aws/rds/cluster/${ProjectName}-${Environment}-aurora-cluster/postgresql'
      RetentionInDays: 90
      KmsKeyId: !Sub 'arn:${AWS::Partition}:kms:${AWS::Region}:${AWS::AccountId}:alias/${ProjectName}-${Environment}-logs'
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-${Environment}-database-logs'
        - Key: Environment
          Value: !Ref Environment

  # SNS Topic for Database Alerts
  DatabaseAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ProjectName}-${Environment}-database-alerts'
      DisplayName: !Sub 'Database Alerts for ${ProjectName} ${Environment}'
      KmsMasterKeyId: !Ref KMSKeyId

  # RDS Event Subscriptions
  DatabaseClusterEventSubscription:
    Type: AWS::RDS::EventSubscription
    Properties:
      SnsTopicArn: !Ref DatabaseAlertsTopic
      SourceType: db-cluster
      SourceIds:
        - !Ref DatabaseCluster
      EventCategories:
        - configuration change
        - creation
        - deletion
        - failover
        - failure
        - maintenance
        - notification

  DatabaseInstanceEventSubscription:
    Type: AWS::RDS::EventSubscription
    Properties:
      SnsTopicArn: !Ref DatabaseAlertsTopic
      SourceType: db-instance
      SourceIds:
        - !Ref DatabasePrimaryInstance
        - !Ref DatabaseReaderInstance1
        - !Ref DatabaseReaderInstance2
      EventCategories:
        - configuration change
        - creation
        - deletion
        - failover
        - failure
        - low storage
        - maintenance
        - notification
        - read replica
        - recovery

  # Lambda Function for Database Bootstrap
  DatabaseBootstrapLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
      Policies:
        - PolicyName: DatabaseBootstrapPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Ref DatabaseSecret
              - Effect: Allow
                Action:
                  - rds:DescribeDBClusters
                  - rds:DescribeDBInstances
                Resource:
                  - !Sub 'arn:${AWS::Partition}:rds:${AWS::Region}:${AWS::AccountId}:cluster:${DatabaseCluster}'
                  - !Sub 'arn:${AWS::Partition}:rds:${AWS::Region}:${AWS::AccountId}:db:${DatabasePrimaryInstance}'

  DatabaseBootstrapLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub '${ProjectName}-${Environment}-database-bootstrap'
      Runtime: python3.11
      Handler: index.lambda_handler
      Role: !GetAtt DatabaseBootstrapLambdaRole.Arn
      Timeout: 300
      ReservedConcurrentExecutions: 5
      DeadLetterConfig:
        TargetArn: !GetAtt DatabaseBootstrapDLQ.Arn
      VpcConfig:
        SecurityGroupIds:
          - !Ref DatabaseSecurityGroupId
        SubnetIds:
          - !Ref DatabaseSubnet1Id
          - !Ref DatabaseSubnet2Id
      Environment:
        Variables:
          DB_CLUSTER_IDENTIFIER: !Ref DatabaseCluster
          DB_SECRET_ARN: !Ref DatabaseSecret
          DB_NAME: !Ref DatabaseName
      KmsKeyArn: !Ref KMSKeyId
      Code:
        ZipFile: |
          import json
          import boto3
          import os
          # Using RDS Data API instead of psycopg2 to avoid dependency issues
          import cfnresponse
          
          def lambda_handler(event, context):
              try:
                  if event['RequestType'] == 'Create':
                      # Get database connection details from Secrets Manager
                      secrets_client = boto3.client('secretsmanager')
                      secret_response = secrets_client.get_secret_value(
                          SecretId=os.environ['DB_SECRET_ARN']
                      )
                      secret = json.loads(secret_response['SecretString'])
                      
                      # Get cluster endpoint
                      rds_client = boto3.client('rds')
                      cluster_response = rds_client.describe_db_clusters(
                          DBClusterIdentifier=os.environ['DB_CLUSTER_IDENTIFIER']
                      )
                      endpoint = cluster_response['DBClusters'][0]['Endpoint']
                      
                      # Connect to database
                      conn = psycopg2.connect(
                          host=endpoint,
                          port=5432,
                          database=os.environ['DB_NAME'],
                          user=secret['username'],
                          password=secret['password']
                      )
                      
                      cursor = conn.cursor()
                      
                      # Create extensions
                      extensions = [
                          'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
                          'CREATE EXTENSION IF NOT EXISTS "pgcrypto";',
                          'CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";',
                          'CREATE EXTENSION IF NOT EXISTS "pgaudit";',
                          'CREATE EXTENSION IF NOT EXISTS "pg_cron";'
                      ]
                      
                      for ext in extensions:
                          cursor.execute(ext)
                      
                      # Create trading schema and tables
                      trading_schema = '''
                      CREATE SCHEMA IF NOT EXISTS trading;
                      
                      CREATE TABLE IF NOT EXISTS trading.orders (
                          order_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                          user_id UUID NOT NULL,
                          symbol VARCHAR(10) NOT NULL,
                          order_type VARCHAR(20) NOT NULL,
                          quantity DECIMAL(18,8) NOT NULL,
                          price DECIMAL(18,8),
                          status VARCHAR(20) NOT NULL DEFAULT 'NEW',
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          risk_score DECIMAL(5,2),
                          compliance_status VARCHAR(20) DEFAULT 'PENDING'
                      ) PARTITION BY RANGE (created_at);
                      
                      CREATE TABLE IF NOT EXISTS trading.orders_2025_01 PARTITION OF trading.orders
                          FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
                      
                      CREATE TABLE IF NOT EXISTS trading.trades (
                          trade_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                          order_id UUID REFERENCES trading.orders(order_id),
                          symbol VARCHAR(10) NOT NULL,
                          executed_quantity DECIMAL(18,8) NOT NULL,
                          executed_price DECIMAL(18,8) NOT NULL,
                          execution_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          counterparty VARCHAR(50),
                          settlement_date DATE,
                          trade_hash VARCHAR(64) UNIQUE
                      );
                      
                      CREATE TABLE IF NOT EXISTS trading.risk_positions (
                          position_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                          user_id UUID NOT NULL,
                          symbol VARCHAR(10) NOT NULL,
                          position_size DECIMAL(18,8) NOT NULL,
                          average_price DECIMAL(18,8) NOT NULL,
                          unrealized_pnl DECIMAL(18,8),
                          risk_exposure DECIMAL(18,8),
                          var_95 DECIMAL(18,8),
                          last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          UNIQUE(user_id, symbol)
                      );
                      
                      -- Create indexes
                      CREATE INDEX IF NOT EXISTS idx_orders_user_status 
                          ON trading.orders (user_id, status) WHERE status IN ('NEW', 'PARTIAL');
                      CREATE INDEX IF NOT EXISTS idx_orders_symbol_time 
                          ON trading.orders (symbol, created_at DESC);
                      CREATE INDEX IF NOT EXISTS idx_trades_execution_time 
                          ON trading.trades (execution_time DESC);
                      CREATE INDEX IF NOT EXISTS idx_risk_positions_user 
                          ON trading.risk_positions (user_id) INCLUDE (position_size, unrealized_pnl);
                      '''
                      
                      cursor.execute(trading_schema)
                      
                      # Create application users with limited privileges
                      app_users = '''
                      CREATE ROLE IF NOT EXISTS trading_api_user WITH LOGIN PASSWORD 'temp_password';
                      CREATE ROLE IF NOT EXISTS trading_readonly_user WITH LOGIN PASSWORD 'temp_password';
                      CREATE ROLE IF NOT EXISTS trading_writer_user WITH LOGIN PASSWORD 'temp_password';
                      
                      GRANT USAGE ON SCHEMA trading TO trading_api_user, trading_readonly_user, trading_writer_user;
                      GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA trading TO trading_api_user;
                      GRANT SELECT ON ALL TABLES IN SCHEMA trading TO trading_readonly_user;
                      GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA trading TO trading_writer_user;
                      GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA trading TO trading_api_user, trading_writer_user;
                      '''
                      
                      cursor.execute(app_users)
                      
                      conn.commit()
                      cursor.close()
                      conn.close()
                      
                      cfnresponse.send(event, context, cfnresponse.SUCCESS, {})
                  else:
                      cfnresponse.send(event, context, cfnresponse.SUCCESS, {})
                      
              except Exception as e:
                  print(f"Error: {str(e)}")
                  cfnresponse.send(event, context, cfnresponse.FAILED, {})

  DatabaseBootstrapDLQ:
    Type: AWS::SQS::Queue
    # DeletionPolicy: Delete
    # UpdateReplacePolicy: Delete
    Properties:
      QueueName: !Sub '${ProjectName}-${Environment}-database-bootstrap-dlq'
      KmsMasterKeyId: !Ref KMSKeyId
      MessageRetentionPeriod: 1209600

  DatabaseBootstrapCustomResource:
    Type: AWS::CloudFormation::CustomResource
    DependsOn: 
      - DatabasePrimaryInstance
      - DatabaseReaderInstance1
    Properties:
      ServiceToken: !GetAtt DatabaseBootstrapLambda.Arn
      DatabaseCluster: !Ref DatabaseCluster

Outputs:
  DatabaseClusterIdentifier:
    Description: Aurora PostgreSQL cluster identifier
    Value: !Ref DatabaseCluster
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseClusterIdentifier'

  DatabaseClusterEndpoint:
    Description: Aurora PostgreSQL cluster endpoint
    Value: !GetAtt DatabaseCluster.Endpoint.Address
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseClusterEndpoint'

  DatabaseClusterReadEndpoint:
    Description: Aurora PostgreSQL cluster read endpoint
    Value: !GetAtt DatabaseCluster.ReadEndpoint.Address
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseClusterReadEndpoint'

  DatabaseProxyEndpoint:
    Description: RDS Proxy endpoint
    Value: !GetAtt DatabaseProxy.Endpoint
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseProxyEndpoint'

  DatabasePort:
    Description: Database port
    Value: !FindInMap [EngineVersionMap, !Ref DatabaseEngine, Port]
    Export:
      Name: !Sub '${AWS::StackName}-DatabasePort'

  DatabaseName:
    Description: Database name
    Value: !Ref DatabaseName
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseName'

  DatabaseSecretArn:
    Description: Database credentials secret ARN
    Value: !Ref DatabaseSecret
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseSecretArn'

  DatabaseSubnetGroupName:
    Description: Database subnet group name
    Value: !Ref DatabaseSubnetGroup
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseSubnetGroupName'

  DatabaseParameterGroupName:
    Description: Database parameter group name
    Value: !Ref DatabaseParameterGroup
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseParameterGroupName'

  DatabaseClusterParameterGroupName:
    Description: Database cluster parameter group name
    Value: !Ref DatabaseClusterParameterGroup
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseClusterParameterGroupName'

  DatabaseAlertsTopicArn:
    Description: Database alerts SNS topic ARN
    Value: !Ref DatabaseAlertsTopic
    Export:
      Name: !Sub '${AWS::StackName}-DatabaseAlertsTopicArn'